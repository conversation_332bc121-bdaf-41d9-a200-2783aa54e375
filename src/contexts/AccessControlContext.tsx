'use client';

import { createContext, useContext, ReactNode } from 'react';
import { useAccessControl } from '@/hooks/useAccessControl';

interface AccessControlContextType {
  isOwner: boolean;
  hasAccess: boolean;
  isLoading: boolean;
  error: Error | null;
}

interface AccessControlProviderProps {
  children: ReactNode;
  listingId: string;
  userId?: string;
}

const AccessControlContext = createContext<AccessControlContextType | undefined>(undefined);

export function AccessControlProvider({ children, listingId, userId }: AccessControlProviderProps) {
  const accessControl = useAccessControl(listingId, userId);

  return (
    <AccessControlContext.Provider value={accessControl}>
      {children}
    </AccessControlContext.Provider>
  );
}

export function useAccessControlContext() {
  const context = useContext(AccessControlContext);
  if (context === undefined) {
    throw new Error('useAccessControlContext must be used within an AccessControlProvider');
  }
  return context;
} 