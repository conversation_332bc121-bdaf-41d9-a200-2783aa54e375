'use client';

import { createContext, useContext, useState, ReactNode } from 'react';

interface ViewModeContextType {
    isPublicView: boolean;
    toggleViewMode: (isPublic: boolean) => void;
}

interface ViewModeProviderProps {
    children: ReactNode;
    initialIsPublic?: boolean;
}

const ViewModeContext = createContext<ViewModeContextType | undefined>(undefined);

export function ViewModeProvider({ children, initialIsPublic = false }: ViewModeProviderProps) {
    const [isPublicView, setIsPublicView] = useState(initialIsPublic);

    const toggleViewMode = (isPublic: boolean) => {
        setIsPublicView(isPublic);
    };

    return (
        <ViewModeContext.Provider value={{ isPublicView, toggleViewMode }}>
            {children}
        </ViewModeContext.Provider>
    );
}

export function useViewMode() {
    const context = useContext(ViewModeContext);
    if (context === undefined) {
        throw new Error('useViewMode must be used within a ViewModeProvider');
    }
    return context;
} 