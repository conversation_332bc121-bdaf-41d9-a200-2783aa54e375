'use client';

import { useState, useEffect } from 'react';
import { useSupabase } from './useSupabase';

interface AccessControlResult {
  isOwner: boolean;
  hasAccess: boolean;
  isLoading: boolean;
  error: Error | null;
}

/**
 * React hook for checking if a user is the owner of a listing or has access to private files
 * @param listingId The ID of the listing to check
 * @param userId The ID of the current user (undefined if not logged in)
 * @returns Access control information: isOwner, hasAccess, isLoading and error state
 */
export function useAccessControl(
  listingId: string | undefined,
  userId: string | undefined
): AccessControlResult {
  const supabase = useSupabase();
  const [result, setResult] = useState<AccessControlResult>({
    isOwner: false,
    hasAccess: false,
    isLoading: true,
    error: null,
  });
  
  useEffect(() => {
    const checkAccess = async () => {
      // Reset state when params change
      setResult({
        isOwner: false,
        hasAccess: false,
        isLoading: true,
        error: null,
      });
      
      // Skip if no listingId or userId
      if (!listingId || !userId) {
        setResult({
          isOwner: false,
          hasAccess: false,
          isLoading: false,
          error: null,
        });
        return;
      }
      
      try {
        // Check if user is the owner
        const { data: listing, error: listingError } = await supabase
          .from('listings')
          .select('user_id')
          .eq('id', listingId)
          .single();
        
        if (listingError) throw listingError;
        
        const isOwner = userId === listing?.user_id;
        
        // If user is owner, they automatically have access
        if (isOwner) {
          setResult({
            isOwner: true,
            hasAccess: true,
            isLoading: false,
            error: null,
          });
          return;
        }
        
        // Check if user has been granted access to private files
        const { data: accessData, error: accessError } = await supabase
          .from('data_room_access')
          .select('id')
          .eq('listing_id', listingId)
          .eq('user_id', userId)
          .maybeSingle();
        
        if (accessError) throw accessError;
        
        setResult({
          isOwner: false,
          hasAccess: !!accessData,
          isLoading: false,
          error: null,
        });
      } catch (error) {
        setResult({
          isOwner: false,
          hasAccess: false,
          isLoading: false,
          error: error instanceof Error ? error : new Error('Unknown error checking access control'),
        });
        console.error('Error checking access control:', error);
      }
    };
    
    checkAccess();
  }, [listingId, userId, supabase]);
  
  return result;
} 