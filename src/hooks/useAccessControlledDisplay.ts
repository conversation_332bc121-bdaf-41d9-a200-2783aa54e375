'use client';

import { useMemo } from 'react';
import { useViewMode } from '@/contexts/ViewModeContext';
import { useAccessControlContext } from '@/contexts/AccessControlContext';

interface ListingAnonymizedDetails {
    anonymous_title?: string | null;
    anonymous_description?: string | null;
    anonymous_image_url?: string | null;
}

interface UseAccessControlledDisplayProps {
    realTitle?: string | null;
    realDescription?: string | null;
    realImageUrl?: string | null;
    anonymizedDetails?: ListingAnonymizedDetails | null;
    fallbacks?: {
        title?: string;
        description?: string;
        imageUrl?: string;
    };
}

interface AccessControlledDisplayValues {
    displayTitle: string;
    displayDescription: string;
    displayImageUrl: string | null;
}

/**
 * Custom hook that provides access-controlled display values for listing data
 * Eliminates duplication of access control logic across components
 */
export function useAccessControlledDisplay({
    realTitle,
    realDescription,
    realImageUrl,
    anonymizedDetails,
    fallbacks = {}
}: UseAccessControlledDisplayProps): AccessControlledDisplayValues {
    const { isPublicView } = useViewMode();
    const { isOwner, hasAccess } = useAccessControlContext();

    const displayTitle = useMemo(() => {
        if (isOwner) {
            return (isPublicView ? anonymizedDetails?.anonymous_title : realTitle) || fallbacks.title || 'No title available';
        }
        if (hasAccess) {
            return realTitle || fallbacks.title || 'No title available';
        }

        return anonymizedDetails?.anonymous_title || fallbacks.title || 'No title available';
    }, [isOwner, hasAccess, isPublicView, realTitle, anonymizedDetails?.anonymous_title, fallbacks.title]);

    const displayDescription = useMemo(() => {
        if (isOwner) {
            return (isPublicView ? anonymizedDetails?.anonymous_description : realDescription) || fallbacks.description || 'No description available';
        }
        if (hasAccess) {
            return realDescription || fallbacks.description || 'No description available';
        }

        return anonymizedDetails?.anonymous_description || fallbacks.description || 'No description available';
    }, [isOwner, hasAccess, isPublicView, realDescription, anonymizedDetails?.anonymous_description, fallbacks.description]);

    const displayImageUrl = useMemo(() => {
        if (isOwner) {
            return (isPublicView ? anonymizedDetails?.anonymous_image_url : realImageUrl) || fallbacks.imageUrl || null;
        }
        if (hasAccess) {
            return realImageUrl || fallbacks.imageUrl || null;
        }

        return anonymizedDetails?.anonymous_image_url || fallbacks.imageUrl || null;
    }, [isOwner, hasAccess, isPublicView, realImageUrl, anonymizedDetails?.anonymous_image_url, fallbacks.imageUrl]);

    return {
        displayTitle,
        displayDescription,
        displayImageUrl
    };
} 