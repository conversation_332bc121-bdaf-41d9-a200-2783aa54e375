'use client';

import { FC, FormEvent, useEffect, useState } from 'react';
import Image from 'next/image';
import { ChevronDown } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import { trackSearch } from '@/utils/analytics';

interface State {
  id: string;
  name: string;
  code: string;
}

const Hero: FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [states, setStates] = useState<State[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStateId, setSelectedStateId] = useState('');
  const supabase = createClient();

  // Get URL search parameters on component mount
  useEffect(() => {
    const currentSearchQuery = searchParams.get('search') || '';
    const currentStateId = searchParams.get('state') || '';

    setSearchQuery(currentSearchQuery);
    setSelectedStateId(currentStateId);
  }, [searchParams]);

  useEffect(() => {
    const fetchStates = async () => {
      const statesResponse = await supabase
        .from('states')
        .select('*')
        .order('name');

      if (!statesResponse.error && statesResponse.data) {
        setStates(statesResponse.data);
      }
    };
    fetchStates();
  }, [supabase]);

  const handleSearch = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const searchQuery = formData.get('search')?.toString() || '';
    const stateId = formData.get('state')?.toString();
    const stateName = states.find(s => s.id === stateId)?.name;

    trackSearch(searchQuery, stateName);

    const params = new URLSearchParams();
    if (searchQuery) params.set('search', searchQuery);
    if (stateId) params.set('state', stateId);

    router.push(`/listings?${params.toString()}`);
  };

  return (
    <div className="relative h-[600px] w-full overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 bg-black">
        <div className="absolute inset-0">
          <Image
            src="/images/hero.jpeg"
            alt="Hero background"
            fill
            className="object-cover brightness-75"
            priority
          />
          {/* Add dark overlay */}
          <div className="absolute inset-0 bg-black/30" />
        </div>
      </div>

      {/* Notification Bar */}
      <div className="relative bg-white/10 backdrop-blur-sm border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 flex items-center justify-center">
          <p className="text-white text-sm flex items-center">
            Are you looking to sell your business?
            <Link
              href="/listings/add-listing"
              className="text-sm bg-neutral-100 text-neutral-700 px-4 py-3 ml-2 rounded-md hover:bg-neutral-200 transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-500 font-[550] tracking-wide"
            >
              Get Started
            </Link>
          </p>
        </div>
      </div>

      {/* Content Overlay - Now using grid for better control */}
      <div className="relative h-[calc(100%-48px)] grid grid-rows-[1fr,auto]">
        {/* Main Content Section */}
        <div className="flex flex-col items-center justify-center px-4">
          <h1 className="text-5xl md:text-6xl font-bold text-center mb-8 text-white mt-8">
            Find a business for sale
          </h1>

          {/* Search Section */}
          <div className="w-full max-w-4xl">
            <form
              onSubmit={handleSearch}
              className="flex flex-col md:flex-row gap-2"
            >
              <div className="flex-1">
                <input
                  type="text"
                  name="search"
                  placeholder="Search businesses"
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg bg-white text-black placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-moss-500"
                />
              </div>
              <div className="flex-1 relative">
                <select
                  name="state"
                  value={selectedStateId}
                  onChange={e => setSelectedStateId(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg bg-white text-black appearance-none focus:outline-none focus:ring-2 focus:ring-moss-500 pr-10"
                >
                  <option value="">All States</option>
                  {states.map(state => (
                    <option key={state.id} value={state.id}>
                      {state.name}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-500 pointer-events-none" />
              </div>
              <button
                type="submit"
                className="bg-neutral-800 hover:bg-neutral-700 text-white px-8 py-3 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-500 font-[550] tracking-wide"
              >
                Search
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
