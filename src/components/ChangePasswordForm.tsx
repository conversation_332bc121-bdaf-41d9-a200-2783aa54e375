'use client'

import { changePassword } from '@/app/account/security/actions'
import Input from '@/components/ui/Input'
import { useState, useRef } from 'react'
import { Eye, EyeOff } from 'lucide-react'
import Link from 'next/link'

export default function ChangePasswordForm() {
    const [error, setError] = useState<string | null>(null)
    const [success, setSuccess] = useState(false)
    const [isLoading, setIsLoading] = useState(false)
    const [showPasswords, setShowPasswords] = useState({
        current: false,
        new: false,
        confirm: false
    })
    const formRef = useRef<HTMLFormElement>(null)

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        setIsLoading(true)
        setError(null)
        setSuccess(false)

        const result = await changePassword(new FormData(e.currentTarget))

        if (result.error) {
            setError(result.error)
        } else if (result.success) {
            setSuccess(true)
            // Reset form using ref
            if (formRef.current) {
                formRef.current.reset()
            }
        }

        setIsLoading(false)
    }

    const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
        setShowPasswords(prev => ({
            ...prev,
            [field]: !prev[field]
        }))
    }

    return (
        <div className="max-w-2xl">
            {error && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-700 text-sm">{error}</p>
                </div>
            )}

            {success && (
                <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <p className="text-gray-700 text-sm">
                        Password updated successfully! Your account is now more secure.
                    </p>
                </div>
            )}

            <form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
                <div className="relative">
                    <Input
                        id="currentPassword"
                        name="currentPassword"
                        type={showPasswords.current ? "text" : "password"}
                        label="Current Password"
                        required
                        placeholder="Enter your current password"
                        disabled={isLoading}
                    />
                    <button
                        type="button"
                        onClick={() => togglePasswordVisibility('current')}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 mt-3 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                        {showPasswords.current ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                </div>

                <div className="relative">
                    <Input
                        id="newPassword"
                        name="newPassword"
                        type={showPasswords.new ? "text" : "password"}
                        label="New Password"
                        required
                        placeholder="Enter your new password"
                        disabled={isLoading}
                        minLength={8}
                    />
                    <button
                        type="button"
                        onClick={() => togglePasswordVisibility('new')}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 mt-3 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                        {showPasswords.new ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                </div>

                <div className="relative">
                    <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type={showPasswords.confirm ? "text" : "password"}
                        label="Confirm New Password"
                        required
                        placeholder="Confirm your new password"
                        disabled={isLoading}
                        minLength={8}
                    />
                    <button
                        type="button"
                        onClick={() => togglePasswordVisibility('confirm')}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 mt-3 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                        {showPasswords.confirm ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Password Requirements:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                        <li>• At least 8 characters long</li>
                        <li>• Must be different from your current password</li>
                        <li>• Should be unique and not used elsewhere</li>
                    </ul>
                </div>

                <div className="flex flex-col sm:flex-row sm:justify-end gap-3 sm:gap-3 pt-4">
                    <Link
                        href="/account"
                        className="flex items-center justify-center w-full sm:w-auto px-8 py-3 rounded-lg font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 border border-gray-300 transition-all duration-200 order-2 sm:order-1"
                    >
                        Cancel
                    </Link>
                    <button
                        type="submit"
                        disabled={isLoading}
                        className="flex items-center justify-center w-full sm:w-auto px-8 py-3 rounded-lg font-medium transition-all duration-200 bg-neutral-800 text-white hover:bg-neutral-700 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed order-1 sm:order-2"
                    >
                        {isLoading ? 'Updating Password...' : 'Update Password'}
                    </button>
                </div>
            </form>
        </div>
    )
} 