'use client';

import { useEffect, useState, useCallback } from 'react';
import { Bell } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import { usePathname, useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatDistanceToNow } from 'date-fns';
import Image from 'next/image';

interface Message {
    id: string;
    created_at: string;
    content: string;
    sender: {
        first_name: string;
        last_name: string;
        profile_photo?: string;
    };
}

interface DataRoomAccessNotification {
    id: string;
    created_at: string;
    listing: {
        id: string;
        title: string;
        slug?: string;
    };
    granted_by: {
        first_name: string;
        last_name: string;
        profile_photo?: string;
    };
}

interface CombinedNotification {
    id: string;
    type: 'message' | 'data_room_access';
    created_at: string;
    message?: Message;
    dataRoomAccess?: DataRoomAccessNotification;
}

export default function NotificationBell({ className = "relative p-2" }: { className?: string }) {
    const [unreadCount, setUnreadCount] = useState(0);
    const [recentNotifications, setRecentNotifications] = useState<CombinedNotification[]>([]);
    const [isAnimating, setIsAnimating] = useState(false);
    const supabase = createClient();
    const pathname = usePathname();
    const router = useRouter();

    const fetchUnreadMessages = useCallback(async () => {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.user) return;

        const { data: basicMessages, error: messagesError } = await supabase
            .from('messages')
            .select('*')
            .eq('recipient_id', session.user.id)
            .eq('read', false)
            .order('created_at', { ascending: false });

        if (messagesError) return;

        // Fetch profiles for message senders
        let formattedMessages: Message[] = [];
        if (basicMessages?.length) {
            const { data: profiles, error: profilesError } = await supabase
                .from('profiles')
                .select('*')
                .in('user_id', basicMessages.map(m => m.sender_id));

            if (!profilesError && profiles) {
                formattedMessages = basicMessages.map(msg => ({
                    id: msg.id,
                    created_at: msg.created_at,
                    content: msg.content,
                    sender: {
                        first_name: profiles?.find(p => p.user_id === msg.sender_id)?.first_name || '',
                        last_name: profiles?.find(p => p.user_id === msg.sender_id)?.last_name || '',
                        profile_photo: profiles?.find(p => p.user_id === msg.sender_id)?.profile_photo
                    }
                }));
            }
        }

        return formattedMessages;
    }, [supabase]);

    const fetchDataRoomAccessNotifications = useCallback(async () => {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.user) return;

        const { data: notifications, error: notificationsError } = await supabase
            .from('data_room_access_notifications')
            .select(`
                id,
                created_at,
                listing_id,
                granted_by,
                read
            `)
            .eq('user_id', session.user.id)
            .eq('read', false)
            .order('created_at', { ascending: false });

        if (notificationsError || !notifications?.length) return [];

        // Fetch listing details
        const { data: listings, error: listingsError } = await supabase
            .from('listings')
            .select('id, title, slug')
            .in('id', notifications.map(n => n.listing_id));

        // Fetch granted_by profiles
        const { data: profiles, error: profilesError } = await supabase
            .from('profiles')
            .select('*')
            .in('user_id', notifications.map(n => n.granted_by));

        if (listingsError || profilesError) return [];

        const formattedNotifications: DataRoomAccessNotification[] = notifications.map(notification => ({
            id: notification.id,
            created_at: notification.created_at,
            listing: {
                id: notification.listing_id,
                title: listings?.find(l => l.id === notification.listing_id)?.title || 'Unknown Listing',
                slug: listings?.find(l => l.id === notification.listing_id)?.slug
            },
            granted_by: {
                first_name: profiles?.find(p => p.user_id === notification.granted_by)?.first_name || '',
                last_name: profiles?.find(p => p.user_id === notification.granted_by)?.last_name || '',
                profile_photo: profiles?.find(p => p.user_id === notification.granted_by)?.profile_photo
            }
        }));

        return formattedNotifications;
    }, [supabase]);

    const fetchAllNotifications = useCallback(async () => {
        const [messages, dataRoomNotifications] = await Promise.all([
            fetchUnreadMessages(),
            fetchDataRoomAccessNotifications()
        ]);

        const combinedNotifications: CombinedNotification[] = [
            ...(messages || []).map(msg => ({
                id: msg.id,
                type: 'message' as const,
                created_at: msg.created_at,
                message: msg
            })),
            ...(dataRoomNotifications || []).map(notification => ({
                id: notification.id,
                type: 'data_room_access' as const,
                created_at: notification.created_at,
                dataRoomAccess: notification
            }))
        ];

        // Sort by created_at descending
        combinedNotifications.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

        setUnreadCount(combinedNotifications.length);
        setRecentNotifications([...combinedNotifications]);
    }, [fetchUnreadMessages, fetchDataRoomAccessNotifications]);

    useEffect(() => {
        const setupRealtimeSubscription = async () => {
            const { data: { session } } = await supabase.auth.getSession();
            if (!session?.user) return;

            // Initial fetch
            await fetchAllNotifications();

            // Subscribe to messages
            const messagesChannel = supabase
                .channel(`messages:${session.user.id}`)
                .on(
                    'postgres_changes',
                    {
                        event: '*',
                        schema: 'public',
                        table: 'messages',
                        filter: `recipient_id=eq.${session.user.id}`,
                    },
                    async (payload) => {
                        if (payload.eventType === 'INSERT') {
                            setIsAnimating(true);
                            await fetchAllNotifications();
                            setTimeout(() => setIsAnimating(false), 200);
                        }
                    }
                )
                .subscribe();

            // Subscribe to data room access notifications
            const dataRoomChannel = supabase
                .channel(`data_room_notifications:${session.user.id}`)
                .on(
                    'postgres_changes',
                    {
                        event: '*',
                        schema: 'public',
                        table: 'data_room_access_notifications',
                        filter: `user_id=eq.${session.user.id}`,
                    },
                    async (payload) => {
                        if (payload.eventType === 'INSERT') {
                            setIsAnimating(true);
                            await fetchAllNotifications();
                            setTimeout(() => setIsAnimating(false), 200);
                        }
                    }
                )
                .subscribe();

            return () => {
                supabase.removeChannel(messagesChannel);
                supabase.removeChannel(dataRoomChannel);
            };
        };

        const subscription = setupRealtimeSubscription();
        const interval = setInterval(fetchAllNotifications, 10000);

        return () => {
            clearInterval(interval);
            subscription.then(cleanup => cleanup?.());
        };
    }, [supabase, pathname, fetchAllNotifications]);

    const handleNotificationClick = async (notification: CombinedNotification) => {
        if (notification.type === 'message') {
            router.push('/account/inbox');
        } else if (notification.type === 'data_room_access' && notification.dataRoomAccess) {
            // Mark as read
            await supabase
                .from('data_room_access_notifications')
                .update({ read: true })
                .eq('id', notification.id);

            // Navigate to the listing data room using the listing ID (not slug)
            const listingId = notification.dataRoomAccess.listing.id;
            router.push(`/listings/${listingId}/data-room`);

            // Refresh notifications
            await fetchAllNotifications();
        }
    };

    const renderNotificationContent = (notification: CombinedNotification) => {
        if (notification.type === 'message' && notification.message) {
            const message = notification.message;
            return (
                <div className="flex items-start gap-3">
                    {message.sender.profile_photo ? (
                        <Image
                            src={message.sender.profile_photo}
                            alt={`${message.sender.first_name || ''} ${message.sender.last_name || ''}`.trim() || 'User'}
                            width={36}
                            height={36}
                            className="h-9 w-9 rounded-full"
                        />
                    ) : (
                        <div className="w-9 h-9 rounded-full bg-gray-200 flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-600">
                                {message.sender.first_name?.[0] || ''}{message.sender.last_name?.[0] || ''}
                            </span>
                        </div>
                    )}
                    <div>
                        <p className="text-sm text-gray-900">
                            <span className="font-semibold">
                                {(message.sender.first_name || message.sender.last_name)
                                    ? `${message.sender.first_name || ''} ${message.sender.last_name || ''}`.trim()
                                    : 'Unknown User'}
                            </span>
                            {' sent you a message'}
                        </p>
                        <div className="flex items-center text-gray-500">
                            <span className="text-sm">{message.content}</span>
                            <span className="text-xs ml-2">
                                {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
                            </span>
                        </div>
                    </div>
                </div>
            );
        } else if (notification.type === 'data_room_access' && notification.dataRoomAccess) {
            const dataRoom = notification.dataRoomAccess;
            return (
                <div className="flex items-start gap-3">
                    {dataRoom.granted_by.profile_photo ? (
                        <Image
                            src={dataRoom.granted_by.profile_photo}
                            alt={`${dataRoom.granted_by.first_name || ''} ${dataRoom.granted_by.last_name || ''}`.trim() || 'User'}
                            width={36}
                            height={36}
                            className="h-9 w-9 rounded-full"
                        />
                    ) : (
                        <div className="w-9 h-9 rounded-full bg-green-200 flex items-center justify-center">
                            <span className="text-sm font-medium text-green-700">
                                {dataRoom.granted_by.first_name?.[0] || ''}{dataRoom.granted_by.last_name?.[0] || ''}
                            </span>
                        </div>
                    )}
                    <div>
                        <p className="text-sm text-gray-900">
                            <span className="font-semibold">
                                {(dataRoom.granted_by.first_name || dataRoom.granted_by.last_name)
                                    ? `${dataRoom.granted_by.first_name || ''} ${dataRoom.granted_by.last_name || ''}`.trim()
                                    : 'Unknown User'}
                            </span>
                            {' granted you access to the data room'}
                        </p>
                        <div className="flex items-center text-gray-500">
                            <span className="text-sm font-medium text-green-600">{dataRoom.listing.title}</span>
                            <span className="text-xs ml-2">
                                {formatDistanceToNow(new Date(dataRoom.created_at), { addSuffix: true })}
                            </span>
                        </div>
                    </div>
                </div>
            );
        }
        return null;
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger className={className}>
                <motion.div
                    animate={isAnimating ? {
                        scale: [1, 1.2, 1],
                        rotate: [0, -10, 10, -10, 0],
                    } : {
                        scale: 1,
                        rotate: 0
                    }}
                    transition={{
                        duration: 0.2,
                        ease: "easeInOut",
                        times: [0, 0.5, 1]
                    }}
                    className="relative inline-block cursor-pointer"
                >
                    <Bell className="w-5 h-5" />
                </motion.div>
                <AnimatePresence mode="wait">
                    {unreadCount > 0 && (
                        <motion.span
                            initial={{ scale: 0.5, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0.5, opacity: 0 }}
                            transition={{ duration: 0.2 }}
                            className="absolute top-1 right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"
                        >
                            <motion.span
                                key={unreadCount}
                                initial={{ scale: 0.5, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                exit={{ scale: 0.5, opacity: 0 }}
                                transition={{ duration: 0.2 }}
                            >
                                {unreadCount > 9 ? '9+' : unreadCount}
                            </motion.span>
                        </motion.span>
                    )}
                </AnimatePresence>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[380px] bg-white p-2">
                {recentNotifications.length > 0 ? (
                    recentNotifications.map((notification) => (
                        <DropdownMenuItem
                            key={notification.id}
                            onClick={() => handleNotificationClick(notification)}
                            className="relative flex select-none items-center text-sm outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 cursor-pointer hover:bg-gray-50 rounded-lg p-4"
                        >
                            {renderNotificationContent(notification)}
                        </DropdownMenuItem>
                    ))
                ) : (
                    <DropdownMenuItem disabled className="text-center text-gray-500 p-4">
                        No new notifications
                    </DropdownMenuItem>
                )}
            </DropdownMenuContent>
        </DropdownMenu>
    );
} 