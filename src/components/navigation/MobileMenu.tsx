'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import {
    Home,
    ListStart,
    Info,
    Mail,
    LogIn,
    LogOut,
    Rocket,
    Menu,
    Plus,
    Search,
    Briefcase,
    Inbox,
} from 'lucide-react'
import { LogoutButton } from '@/components'
import { useListingFormModal } from '@/contexts/ListingFormModalContext'
import type { UserRole } from '@/types/supabase'

interface MobileMenuProps {
    user: {
        id: string;
        email?: string;
        // Add other user properties you actually use
    } | null;
    userRole: UserRole | null;
    initials: string;
    isListingsActive: boolean;
    profilePhotoUrl: string | null;
}

export default function MobileMenu({ user, userRole, initials, isListingsActive, profilePhotoUrl }: MobileMenuProps) {
    const [isOpen, setIsOpen] = useState(false)
    const { openModal } = useListingFormModal()
    const isMatchActive = typeof window !== 'undefined' && window.location.pathname.startsWith('/match')

    // Helper function to check if user can access buyer features
    const canAccessBuyerFeatures = userRole === 'buyer' || userRole === 'seller_buyer';

    return (
        <div className="relative">
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="text-muted-foreground hover:text-primary p-2"
            >
                <Menu className="h-6 w-6" />
            </button>

            {isOpen && (
                <div className="fixed top-16 left-0 right-0 bg-gray-100 shadow-lg z-50">
                    {/* PRIMARY NAVIGATION - Thumb-friendly top section */}
                    <div className="px-4 py-4 space-y-2">
                        <Link href="/"
                            onClick={() => setIsOpen(false)}
                            className="flex items-center space-x-3 w-full px-4 py-3 text-lg font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                        >
                            <Home className="w-6 h-6" />
                            <span>Home</span>
                        </Link>

                        <Link href="/listings"
                            onClick={() => setIsOpen(false)}
                            className={`flex items-center space-x-3 w-full px-4 py-3 text-lg font-medium rounded-lg transition-colors
                                ${isListingsActive
                                    ? 'text-blue-600 bg-blue-50 border border-blue-200'
                                    : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
                                }`}
                        >
                            <ListStart className="w-6 h-6" />
                            <span>Browse Businesses</span>
                        </Link>

                        {/* Only show Match for buyers and seller+buyers */}
                        {(!user || canAccessBuyerFeatures) && (
                            <Link href="/match"
                                onClick={() => setIsOpen(false)}
                                className={`flex items-center space-x-3 w-full px-4 py-3 text-lg font-medium rounded-lg transition-colors
                                    ${isMatchActive
                                        ? 'text-blue-600 bg-blue-50 border border-blue-200'
                                        : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
                                    }`}
                            >
                                <Search className="w-6 h-6" />
                                <span>Find Matches</span>
                            </Link>
                        )}
                    </div>

                    {user ? (
                        <>
                            {/* CTA SECTION - Prominent placement */}
                            <div className="px-4 pb-4">
                                <button
                                    onClick={() => {
                                        setIsOpen(false)
                                        openModal()
                                    }}
                                    className="flex items-center justify-center w-full bg-neutral-800 text-white px-6 py-4 rounded-xl text-lg font-bold hover:bg-neutral-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-[1.02]"
                                >
                                    <Plus className="w-6 h-6 mr-3" />
                                    List Your Business
                                </button>
                            </div>

                            {/* ACCOUNT SECTION */}
                            <div className="border-t border-gray-200 px-4 py-4 space-y-2">
                                <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide px-4 mb-3">Account</div>

                                <Link href="/account"
                                    onClick={() => setIsOpen(false)}
                                    className="flex items-center space-x-3 w-full px-4 py-3 text-lg font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                                >
                                    {profilePhotoUrl ? (
                                        <div className="relative w-7 h-7 rounded-full overflow-hidden">
                                            <Image
                                                src={profilePhotoUrl}
                                                alt="Profile"
                                                fill
                                                className="object-cover"
                                            />
                                        </div>
                                    ) : (
                                        <div className="w-7 h-7 rounded-full bg-gray-300 text-gray-700 flex items-center justify-center text-sm font-semibold">
                                            {initials}
                                        </div>
                                    )}
                                    <span>Profile</span>
                                </Link>

                                <Link href="/account/inbox"
                                    onClick={() => setIsOpen(false)}
                                    className="flex items-center space-x-3 w-full px-4 py-3 text-lg font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                                >
                                    <Inbox className="w-6 h-6" />
                                    <span>Inbox</span>
                                </Link>

                                <Link href="/deals-dashboard"
                                    onClick={() => setIsOpen(false)}
                                    className="flex items-center space-x-3 w-full px-4 py-3 text-lg font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                                >
                                    <Briefcase className="w-6 h-6" />
                                    <span>Deals</span>
                                </Link>
                            </div>

                            {/* SECONDARY NAVIGATION */}
                            <div className="border-t border-gray-200 px-4 py-4 space-y-1">
                                <Link href="/about"
                                    onClick={() => setIsOpen(false)}
                                    className="flex items-center space-x-3 w-full px-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors"
                                >
                                    <Info className="w-5 h-5" />
                                    <span>About</span>
                                </Link>

                                <Link href="/contact"
                                    onClick={() => setIsOpen(false)}
                                    className="flex items-center space-x-3 w-full px-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors"
                                >
                                    <Mail className="w-5 h-5" />
                                    <span>Contact</span>
                                </Link>

                                <LogoutButton>
                                    <div className="flex items-center space-x-3 w-full px-4 py-2 text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors mt-4">
                                        <LogOut className="w-5 h-5" />
                                        <span>Sign Out</span>
                                    </div>
                                </LogoutButton>
                            </div>
                        </>
                    ) : (
                        /* AUTHENTICATION SECTION */
                        <div className="px-4 py-4 space-y-3">
                            <Link href="/signup"
                                onClick={() => setIsOpen(false)}
                                className="flex items-center justify-center w-full bg-neutral-800 text-white px-6 py-4 rounded-xl text-lg font-bold hover:bg-neutral-700 transition-all duration-200 shadow-md"
                            >
                                <Rocket className="w-6 h-6 mr-3" />
                                Get Started
                            </Link>

                            <Link href="/login"
                                onClick={() => setIsOpen(false)}
                                className="flex items-center justify-center w-full border-2 border-gray-300 text-gray-700 px-6 py-3 rounded-xl text-lg font-semibold hover:bg-gray-50 transition-colors"
                            >
                                <LogIn className="w-5 h-5 mr-3" />
                                Sign In
                            </Link>

                            {/* SECONDARY NAVIGATION FOR GUESTS */}
                            <div className="border-t border-gray-200 pt-4 mt-6 space-y-1">
                                <Link href="/about"
                                    onClick={() => setIsOpen(false)}
                                    className="flex items-center space-x-3 w-full px-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors"
                                >
                                    <Info className="w-5 h-5" />
                                    <span>About</span>
                                </Link>

                                <Link href="/contact"
                                    onClick={() => setIsOpen(false)}
                                    className="flex items-center space-x-3 w-full px-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors"
                                >
                                    <Mail className="w-5 h-5" />
                                    <span>Contact</span>
                                </Link>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    )
} 