'use client';

import { useAccessControlledDisplay } from '@/hooks/useAccessControlledDisplay';

interface AccessControlledTextProps {
    realText?: string | null;
    anonymousText?: string | null;
    fallback?: string;
    className?: string;
    as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
    children?: never; // Prevent children to avoid confusion
}

/**
 * Reusable component for displaying access-controlled text (titles, descriptions, etc.)
 * Automatically handles the access control logic based on user permissions
 */
export function AccessControlledText({
    realText,
    anonymousText,
    fallback = 'No information available',
    className = '',
    as: Component = 'span'
}: AccessControlledTextProps) {
    const { displayTitle } = useAccessControlledDisplay({
        realTitle: realText,
        anonymizedDetails: { anonymous_title: anonymousText },
        fallbacks: { title: fallback }
    });

    return <Component className={className}>{displayTitle}</Component>;
} 