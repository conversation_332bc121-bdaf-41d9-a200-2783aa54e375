'use client';

import Image from 'next/image';
import { useAccessControlledDisplay } from '@/hooks/useAccessControlledDisplay';

interface AccessControlledImageProps {
    realImageUrl?: string | null;
    anonymousImageUrl?: string | null;
    alt: string;
    fallbackImageUrl?: string;
    className?: string;
    fill?: boolean;
    width?: number;
    height?: number;
    sizes?: string;
    priority?: boolean;
}

/**
 * Reusable component for displaying access-controlled images
 * Automatically handles the access control logic based on user permissions
 */
export function AccessControlledImage({
    realImageUrl,
    anonymousImageUrl,
    alt,
    fallbackImageUrl = '/images/placeholder.png',
    className = '',
    fill,
    width,
    height,
    sizes,
    priority
}: AccessControlledImageProps) {
    const { displayImageUrl } = useAccessControlledDisplay({
        realImageUrl,
        anonymizedDetails: { anonymous_image_url: anonymousImageUrl },
        fallbacks: { imageUrl: fallbackImageUrl }
    });

    const imageProps = {
        src: displayImageUrl || fallbackImageUrl,
        alt,
        className,
        priority,
        sizes,
        ...(fill ? { fill: true } : { width, height })
    };

    return <Image {...imageProps} alt={alt} />;
} 