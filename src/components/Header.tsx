'use client';

import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import {
  ListStart,
  Info,
  Mail,
  LogIn,
  Rocket,
  User,
  ChevronDown,
  LogOut,
  Bookmark,
  Inbox,
  Briefcase,
  Search,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { LogoutButton } from '@/components';
import { MobileMenu } from '@/components';
import ListBusinessButton from '@/components/Header/ListBusinessButton';
import NotificationBell from '@/components/notifications/NotificationBell';
import { createClient } from '@/utils/supabase/client';
import Image from 'next/image';
import type { User as SupabaseUser } from '@supabase/auth-js';
import type { UserRole } from '@/types/supabase';

type UserProfile = {
  user_id: string;
  first_name: string | null;
  last_name: string | null;
  profile_photo: string | null;
  user_role: UserRole | null;
};

export default function Header() {
  const pathname = usePathname();
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [initials, setInitials] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const supabase = createClient();

  const isListingsActive = pathname.startsWith('/listings');
  const isMatchActive = pathname.startsWith('/match');

  // Fetch user and profile data
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const {
          data: { user: currentUser },
        } = await supabase.auth.getUser();
        setUser(currentUser);

        if (currentUser) {
          // Fetch profile data
          const { data: profileData } = await supabase
            .from('profiles')
            .select('user_id, first_name, last_name, profile_photo, user_role')
            .eq('user_id', currentUser.id)
            .single();

          setProfile(profileData);
        }
      } catch (error) {
        console.error('Error fetching user:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUser();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_IN') {
        setUser(session?.user || null);
        // Refresh profile data when user signs in
        if (session?.user) {
          supabase
            .from('profiles')
            .select('user_id, first_name, last_name, profile_photo, user_role')
            .eq('user_id', session.user.id)
            .single()
            .then(({ data }) => setProfile(data));
        }
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
        setProfile(null);
      }
    });

    return () => subscription.unsubscribe();
  }, [supabase]);

  // Update initials when profile changes
  useEffect(() => {
    if (profile?.first_name && profile?.last_name) {
      setInitials(
        `${profile.first_name[0] || ''}${
          profile.last_name[0] || ''
        }`.toUpperCase()
      );
    } else if (profile?.first_name) {
      setInitials(`${profile.first_name[0] || ''}`.toUpperCase());
    } else if (profile?.last_name) {
      setInitials(`${profile.last_name[0] || ''}`.toUpperCase());
    } else {
      setInitials('');
    }
  }, [profile]);

  // Helper functions to check user roles
  const userRole = profile?.user_role || 'seller_buyer';
  const canAccessSellerFeatures =
    userRole === 'seller' || userRole === 'seller_buyer';
  const canAccessBuyerFeatures =
    userRole === 'buyer' || userRole === 'seller_buyer';

  // Show loading state briefly
  if (isLoading) {
    return (
      <header className="sticky top-0 z-40 bg-gray-100 border-b border-border border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex-shrink-0">
              <Link href="/" className="flex items-center space-x-2">
                <Image
                  src="/images/evermark_site_logo.png"
                  alt="Evermark"
                  width={240}
                  height={64}
                  className="h-10 w-auto"
                />
              </Link>
            </div>
            <div className="animate-pulse">
              <div className="w-24 h-8 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </header>
    );
  }

  return (
    <header className="sticky top-0 z-40 bg-gray-100 border-b border-border border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center space-x-2">
              <Image
                src="/images/evermark_site_logo.png"
                alt="Evermark"
                width={240}
                height={64}
                className="h-10 w-auto"
              />
            </Link>
          </div>

          <nav className="hidden md:flex space-x-8">
            <Link
              href="/listings"
              className={`flex items-center space-x-2 px-3 py-2 text-sm font-medium ${
                isListingsActive
                  ? 'text-blue-500 hover:text-blue-600'
                  : 'text-gray-800 hover:text-primary'
              }`}
            >
              <ListStart
                className={`w-4 h-4 ${isListingsActive ? 'text-blue-500' : ''}`}
              />
              <span>Listings</span>
            </Link>
            {/* Only show Match for authenticated users with buyer privileges */}
            {user && canAccessBuyerFeatures && (
              <Link
                href="/match"
                className={`flex items-center space-x-2 px-3 py-2 text-sm font-medium ${
                  isMatchActive
                    ? 'text-blue-500 hover:text-blue-600'
                    : 'text-gray-800 hover:text-primary'
                }`}
              >
                <Search
                  className={`w-4 h-4 ${isMatchActive ? 'text-blue-500' : ''}`}
                />
                <span>Match</span>
              </Link>
            )}

            {/* Only show Deals Dashboard for authenticated users */}
            {user && (
              <Link
                href="/deals-dashboard"
                className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-800 hover:text-primary"
              >
                <Briefcase className="w-4 h-4" />
                <span>Deals</span>
              </Link>
            )}

            <Link
              href="/about"
              className="flex items-center space-x-2 text-gray-800 hover:text-primary px-3 py-2 text-sm font-medium"
            >
              <Info className="w-4 h-4" />
              <span>About</span>
            </Link>
            <Link
              href="/contact"
              className="flex items-center space-x-2 text-gray-800 hover:text-primary px-3 py-2 text-sm font-medium"
            >
              <Mail className="w-4 h-4" />
              <span>Contact</span>
            </Link>
          </nav>

          <div className="hidden md:flex items-center space-x-4">
            {!user ? (
              <>
                <Link
                  href="/login"
                  className="flex items-center space-x-2 text-gray-800 hover:text-primary px-3 py-2 text-sm font-medium"
                >
                  <LogIn className="w-4 h-4" />
                  <span>Sign in</span>
                </Link>
                <Link
                  href="/signup"
                  className="flex items-center space-x-2 bg-neutral-800 text-white px-4 py-3 rounded-md hover:bg-neutral-700 transition-colors font-[550] tracking-wide"
                >
                  <Rocket className="w-4 h-4" />
                  <span>Get started</span>
                </Link>
              </>
            ) : (
              <>
                <div className="relative">
                  <NotificationBell />
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger className="flex items-center space-x-2 text-gray-800 hover:text-primary px-3 py-2 text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      {profile?.profile_photo ? (
                        <div className="relative w-6 h-6 rounded-full overflow-hidden">
                          <Image
                            src={profile.profile_photo}
                            alt="Profile"
                            fill
                            className="object-cover"
                          />
                        </div>
                      ) : (
                        <div className="w-6 h-6 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center text-sm font-medium">
                          {initials}
                        </div>
                      )}
                      <span>Account</span>
                      <ChevronDown className="w-4 h-4" />
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    align="end"
                    className="w-[200px] bg-white"
                  >
                    <Link href="/account">
                      <DropdownMenuItem className="cursor-pointer">
                        <User className="w-4 h-4 mr-2" />
                        <span>Profile</span>
                      </DropdownMenuItem>
                    </Link>

                    <Link href="/account/inbox">
                      <DropdownMenuItem className="cursor-pointer">
                        <Inbox className="w-4 h-4 mr-2" />
                        <span>Inbox</span>
                      </DropdownMenuItem>
                    </Link>

                    {/* Only show My Listings for sellers and seller+buyers */}
                    {canAccessSellerFeatures && (
                      <Link href="/account/my-listings">
                        <DropdownMenuItem className="cursor-pointer">
                          <ListStart className="w-4 h-4 mr-2" />
                          <span>My Listings</span>
                        </DropdownMenuItem>
                      </Link>
                    )}

                    {/* Only show Saved Listings for buyers and seller+buyers */}
                    {canAccessBuyerFeatures && (
                      <Link href="/account/saved-listings">
                        <DropdownMenuItem className="cursor-pointer">
                          <Bookmark className="w-4 h-4 mr-2" />
                          <span>Saved Listings</span>
                        </DropdownMenuItem>
                      </Link>
                    )}

                    <Link href="/deals-dashboard">
                      <DropdownMenuItem className="cursor-pointer">
                        <Briefcase className="w-4 h-4 mr-2" />
                        <span>Deals Dashboard</span>
                      </DropdownMenuItem>
                    </Link>

                    <DropdownMenuSeparator />

                    <LogoutButton>
                      <DropdownMenuItem className="cursor-pointer text-red-600 focus:text-red-600">
                        <LogOut className="w-4 h-4 mr-2" />
                        <span>Sign out</span>
                      </DropdownMenuItem>
                    </LogoutButton>
                  </DropdownMenuContent>
                </DropdownMenu>

                <ListBusinessButton user={user} />
              </>
            )}
          </div>

          <div className="md:hidden flex items-center justify-center space-x-2">
            <Link
              href="/account/inbox"
              className="text-gray-800 hover:text-primary p-2"
            >
              <div className="relative">
                <NotificationBell />
              </div>
            </Link>

            <MobileMenu
              user={user}
              userRole={userRole}
              initials={initials}
              isListingsActive={isListingsActive}
              profilePhotoUrl={profile?.profile_photo || null}
            />
          </div>
        </div>
      </div>
    </header>
  );
}
