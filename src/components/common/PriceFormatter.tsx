'use client';

interface PriceFormatterProps {
  price: number;
  className?: string;
}

export function PriceFormatter({ price, className }: PriceFormatterProps) {
  if (price === 0) {
    return <span className={className}>Not Disclosed</span>;
  }

  const options: Intl.NumberFormatOptions = {
    style: 'currency',
    currency: 'USD',
  };

  if (price % 1 === 0) {
    // Check if price is a whole number
    options.minimumFractionDigits = 0;
    options.maximumFractionDigits = 0;
  } else {
    options.minimumFractionDigits = 2;
    options.maximumFractionDigits = 2;
  }

  return (
    <span className={className}>{price.toLocaleString('en-US', options)}</span>
  );
}
