export interface Profile {
    user_id: string;
    first_name: string | null;
    last_name: string | null;
    profile_photo?: string;
    email?: string;
}

export interface Listing {
    id: string;
    user_id: string;
    title: string;
    description: string | null;
    price: number;
    website: string | null;
    image_url: string | null;
    created_at: string;
    listing_details?: Array<{
        street_address: string | null;
        city: string | null;
        postal_code: string | null;
        states: {
            id: string;
            name: string;
            code: string;
        } | null;
    }>;
}

export interface ListingDetails {
    state_id: string;
    street_address: string;
    city: string;
    postal_code?: string;
    states: {
        id: string;
        name: string;
        code: string;
    }[];
}

export interface ListingAnonymizedDetails {
    anonymous_title: string;
    anonymous_description: string | null;
    anonymous_image_url: string | null;
}

export type ListingWithProfile = {
    id: string;
    title: string;
    description: string | null;
    price: number;
    created_at: string;
    industries?: {
        id: string;
        name: string;
    } | null;
    user_id: string;
    website: string | null;
    image_url: string | null;
    listing_details?: Array<{
        street_address: string | null;
        city: string | null;
        postal_code: string | null;
        states: {
            id: string;
            name: string;
            code: string;
        } | null;
    }>;
    profiles?: Profile;
    listing_anonymized_details?: ListingAnonymizedDetails;
}

export interface CreateListingDTO {
    title: string;
    description: string;
    price: number;
    website?: string;
    industry: string;
}

export interface Interest {
    listingId: string;
    userId: string;
    createdAt: string;
    listing: {
        title: string;
        user: {
            profile: {
                first_name: string;
                last_name: string;
                profile_photo: string | null;
            }
        }
    };
}
