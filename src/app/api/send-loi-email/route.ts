import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

const RESEND_API_KEY = process.env.RESEND_API_KEY;

export async function POST(request: NextRequest) {
  try {
    console.log(
      '🔥 LOI EMAIL API CALLED - Letter of Intent notification request received'
    );

    if (!RESEND_API_KEY) {
      console.error('❌ RESEND_API_KEY environment variable not set');
      return NextResponse.json(
        { error: 'RESEND_API_KEY not configured' },
        { status: 500 }
      );
    }

    const body = await request.json();
    console.log('📦 Request body:', JSON.stringify(body, null, 2));

    const { messageId, attachmentData } = body;

    if (!messageId || !attachmentData) {
      console.error('❌ Missing required data: messageId or attachmentData');
      return NextResponse.json(
        { error: 'Missing required data' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Get message details
    const { data: message, error: messageError } = await supabase
      .from('messages')
      .select('sender_id, recipient_id, listing_id, content')
      .eq('id', messageId)
      .single();

    if (messageError || !message) {
      console.error('❌ Failed to fetch message:', messageError);
      return NextResponse.json(
        { error: 'Failed to fetch message' },
        { status: 400 }
      );
    }

    console.log('📧 Message data:', message);

    // Skip if no recipient_id (prevents notifications for system messages)
    if (!message.recipient_id) {
      console.log('⏭️ Skipping - no recipient_id');
      return NextResponse.json({ success: true, skipped: 'no_recipient' });
    }

    // Get recipient profile and check email preferences (listing owner)
    const { data: recipient, error: recipientError } = await supabase
      .from('profiles')
      .select('email, first_name, email_notifications')
      .eq('user_id', message.recipient_id)
      .single();

    console.log('👤 Recipient query result:', { recipient, recipientError });

    if (recipientError || !recipient) {
      console.error('❌ Failed to fetch recipient profile:', recipientError);
      return NextResponse.json(
        { error: 'Failed to fetch recipient profile' },
        { status: 400 }
      );
    }

    // Check if user has email notifications disabled
    if (recipient.email_notifications === false) {
      console.log('🔕 User has email notifications disabled');
      return NextResponse.json({
        success: true,
        skipped: 'notifications_disabled',
      });
    }

    // Get sender profile (buyer who sent the LOI)
    const { data: sender } = await supabase
      .from('profiles')
      .select('first_name, last_name, company')
      .eq('user_id', message.sender_id)
      .single();

    console.log('👤 Sender profile:', sender);

    // Get listing details
    const { data: listing } = await supabase
      .from('listings')
      .select('title, price')
      .eq('id', message.listing_id)
      .single();

    console.log('🏠 Listing details:', listing);

    // Create email content
    const senderName =
      sender?.first_name && sender?.last_name
        ? `${sender.first_name} ${sender.last_name}`
        : sender?.first_name || 'Someone';

    const senderCompany = sender?.company ? ` from ${sender.company}` : '';
    const listingTitle = listing?.title || 'a listing';
    const listingPrice = listing?.price
      ? `$${listing.price.toLocaleString()}`
      : '';
    const fileName = attachmentData.file_name || 'Letter_of_Intent.pdf';
    const fileSize = attachmentData.file_size
      ? `${(attachmentData.file_size / 1024).toFixed(1)} KB`
      : '';

    // For testing purposes, send to your email
    const testEmail = '<EMAIL>';

    console.log(
      `📧 Sending LOI email to test address: ${testEmail} (original: ${recipient.email})`
    );

    // Send email using Resend
    const emailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'Evermark Offers <<EMAIL>>',
        to: testEmail, // Send to test email for debugging
        subject: `📄 Letter of Intent received from ${senderName} for ${listingTitle}`,
        html: `
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Letter of Intent Received</title>
            </head>
            <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #111827; background-color: #f9fafb;">
              
              <!-- Main Container -->
              <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
                
                <!-- Header Section -->
                <div style="background: linear-gradient(135deg, #059669 0%, #047857 100%); padding: 40px 32px; text-align: center; border-radius: 0;">
                  <div style="margin-bottom: 16px;">
                    <div style="display: inline-block; background: rgba(255, 255, 255, 0.2); padding: 12px; border-radius: 12px;">
                      <div style="width: 32px; height: 32px; background: #ffffff; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                        <span style="color: #059669; font-size: 18px; font-weight: bold;">📄</span>
                      </div>
                    </div>
                  </div>
                  <h1 style="margin: 0; font-size: 32px; font-weight: 700; color: #ffffff; letter-spacing: -0.025em;">Letter of Intent Received</h1>
                  <p style="margin: 12px 0 0 0; color: rgba(255, 255, 255, 0.9); font-size: 16px; font-weight: 400;">A potential buyer has submitted an offer for your listing</p>
                </div>
                
                <!-- Content Section -->
                <div style="padding: 32px;">
                  <h2 style="color: #111827; margin: 0 0 16px 0; font-size: 24px; font-weight: 600;">Hello ${
                    recipient.first_name || 'there'
                  }!</h2>
                  
                  <p style="margin: 0 0 24px 0; color: #374151; font-size: 16px; line-height: 1.625;">Great news! You've received a Letter of Intent from <strong>${senderName}${senderCompany}</strong> for your listing:</p>
                  
                  <!-- Listing Card -->
                  <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); border-radius: 12px; padding: 24px; margin: 24px 0; border: 1px solid #bbf7d0;">
                    <h3 style="margin: 0 0 8px 0; color: #111827; font-size: 20px; font-weight: 600;">${listingTitle}</h3>
                    ${
                      listingPrice
                        ? `<p style="margin: 0 0 16px 0; color: #059669; font-size: 18px; font-weight: 600;">${listingPrice}</p>`
                        : ''
                    }
                    <div style="display: flex; align-items: center; gap: 8px; margin-top: 12px;">
                      <span style="display: inline-block; background: #059669; color: white; font-size: 12px; font-weight: 600; padding: 4px 8px; border-radius: 4px; text-transform: uppercase; letter-spacing: 0.05em;">Letter of Intent</span>
                    </div>
                  </div>

                  <!-- LOI Details Card -->
                  <div style="background-color: #ffffff; border-radius: 12px; padding: 24px; margin: 24px 0; border: 1px solid #e5e7eb; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                      <div style="flex-shrink: 0; width: 40px; height: 40px; background: #059669; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                        <span style="color: white; font-size: 16px;">📄</span>
                      </div>
                      <div style="flex: 1;">
                        <h4 style="margin: 0 0 4px 0; color: #111827; font-size: 16px; font-weight: 600;">${fileName}</h4>
                        <p style="margin: 0; color: #6b7280; font-size: 14px;">From: ${senderName}${senderCompany}</p>
                      </div>
                      ${
                        fileSize
                          ? `<div style="text-align: right; color: #6b7280; font-size: 12px;">${fileSize}</div>`
                          : ''
                      }
                    </div>
                    
                    <div style="background-color: #f9fafb; border-radius: 8px; padding: 16px; border-left: 4px solid #059669;">
                      <p style="margin: 0; color: #374151; font-size: 14px; line-height: 1.5;">
                        <strong>💡 Next Steps:</strong> Review the Letter of Intent carefully and consider responding through our platform. This formal offer indicates serious buyer interest in your business.
                      </p>
                    </div>
                  </div>

                  <!-- CTA Buttons -->
                  <div style="text-align: center; margin: 32px 0;">
                    <a href="https://app.evermark.ai/account/inbox" 
                       style="display: inline-block; background-color: #059669; color: #ffffff; padding: 14px 28px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; margin-right: 12px; transition: background-color 0.2s;">
                      View & Download LOI →
                    </a>
                    <a href="https://app.evermark.ai/account/inbox" 
                       style="display: inline-block; background-color: #f3f4f6; color: #374151; padding: 14px 28px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; transition: background-color 0.2s;">
                      Reply to Buyer
                    </a>
                  </div>

                  <!-- Important Notice -->
                  <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border-radius: 8px; padding: 20px; margin: 24px 0; border-left: 4px solid #f59e0b;">
                    <h4 style="margin: 0 0 8px 0; color: #92400e; font-size: 16px; font-weight: 600;">📈 Business Sale Process</h4>
                    <p style="margin: 0; color: #92400e; font-size: 14px; line-height: 1.5;">
                      Letters of Intent are formal offers that typically include purchase price, terms, and conditions. We recommend consulting with a business broker or attorney before proceeding with negotiations.
                    </p>
                  </div>

                  <!-- Security Note -->
                  <div style="background-color: #fef2f2; border-radius: 8px; padding: 16px; margin: 24px 0; border-left: 4px solid #ef4444;">
                    <p style="margin: 0; color: #dc2626; font-size: 14px;">
                      <strong>🛡️ Security Reminder:</strong> Always verify buyer credentials and conduct due diligence. Never share sensitive business information until proper NDAs are signed.
                    </p>
                  </div>

                  <!-- Footer -->
                  <div style="margin-top: 40px; padding-top: 24px; border-top: 1px solid #e5e7eb;">
                    <p style="color: #6b7280; font-size: 14px; text-align: center; margin: 0 0 16px 0; line-height: 1.5;">
                      You received this email because you have an active listing on Evermark.<br>
                      <a href="https://app.evermark.ai/profile" style="color: #2563eb; text-decoration: none;">Manage your notification preferences</a>
                    </p>

                    <p style="color: #6b7280; font-size: 13px; text-align: center; margin: 0;">
                      Best regards,<br>
                      <strong style="color: #111827;">The Evermark Team</strong>
                    </p>
                  </div>
                </div>
              </div>
            </body>
          </html>
        `,
      }),
    });

    console.log('📧 Resend API response status:', emailResponse.status);

    if (!emailResponse.ok) {
      const emailError = await emailResponse.json();
      console.error(
        '❌ Failed to send LOI email - Resend API error:',
        emailError
      );
      return NextResponse.json(
        {
          error: 'Failed to send email',
          details: emailError,
          status: emailResponse.status,
        },
        { status: 500 }
      );
    }

    const emailResult = await emailResponse.json();
    console.log('✅ LOI Email sent successfully:', emailResult);

    return NextResponse.json({
      success: true,
      email_id: emailResult.id,
      recipient_email: testEmail,
      original_recipient: recipient.email,
      type: 'letter_of_intent',
    });
  } catch (error) {
    console.error('❌ LOI email notification error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
