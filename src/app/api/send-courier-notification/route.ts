import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { sendMessageNotification, sendDataRoomAccessNotification } from '@/lib/courier';

export async function POST(request: NextRequest) {
    console.log('🚀 [COURIER API] Starting notification request...');
    try {
        const body = await request.json();
        const { type, data } = body;
        console.log('📥 [COURIER API] Request data:', { type, data });

        // Verify the request is coming from a logged-in user
        const supabase = await createClient();
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError || !session?.user) {
            console.log('❌ [COURIER API] Authentication failed:', sessionError);
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        console.log('✅ [COURIER API] User authenticated:', session.user.id);

        if (type === 'message') {
            console.log('💬 [COURIER API] Processing message notification...');
            const {
                recipientUserId,
                messageContent,
                listingId
            } = data;

            console.log('📋 [COURIER API] Message data:', { recipientUserId, messageContent, listingId });

            // Get recipient profile and listing info
            console.log('🔍 [COURIER API] Fetching recipient, listing, and sender data...');
            const [recipientResponse, listingResponse, senderResponse] = await Promise.all([
                supabase
                    .from('profiles')
                    .select('user_id, first_name, last_name, email')
                    .eq('user_id', recipientUserId)
                    .single(),
                supabase
                    .from('listings')
                    .select('id, title')
                    .eq('id', listingId)
                    .single(),
                supabase
                    .from('profiles')
                    .select('first_name, last_name, profile_photo')
                    .eq('user_id', session.user.id)
                    .single()
            ]);

            console.log('📊 [COURIER API] Database responses:', {
                recipient: recipientResponse.data,
                listing: listingResponse.data,
                sender: senderResponse.data,
                errors: {
                    recipient: recipientResponse.error,
                    listing: listingResponse.error,
                    sender: senderResponse.error
                }
            });

            if (recipientResponse.error || !recipientResponse.data) {
                console.log('❌ [COURIER API] Recipient not found:', recipientResponse.error);
                return NextResponse.json({ error: 'Recipient not found' }, { status: 404 });
            }

            if (listingResponse.error || !listingResponse.data) {
                console.log('❌ [COURIER API] Listing not found:', listingResponse.error);
                return NextResponse.json({ error: 'Listing not found' }, { status: 404 });
            }

            const recipient = recipientResponse.data;
            const listing = listingResponse.data;
            const sender = senderResponse.data;

            const senderName = sender 
                ? `${sender.first_name || ''} ${sender.last_name || ''}`.trim() || 'Unknown User'
                : 'Unknown User';

            console.log('📤 [COURIER API] Preparing to send notification via Courier...');
            const notificationData = {
                recipientUserId: recipient.user_id,
                recipientEmail: recipient.email,
                senderName,
                senderProfilePhoto: sender?.profile_photo,
                messageContent,
                listingTitle: listing.title,
                listingId: listing.id,
            };
            console.log('📋 [COURIER API] Notification data:', notificationData);

            const result = await sendMessageNotification(notificationData);
            console.log('🎯 [COURIER API] Courier result:', result);

            return NextResponse.json(result);

        } else if (type === 'data_room_access') {
            console.log('🏠 [COURIER API] Processing data room access notification...');
            const {
                recipientUserId,
                listingId
            } = data;

            console.log('📋 [COURIER API] Data room access data:', { recipientUserId, listingId });

            // Get recipient profile, listing info, and granter info
            console.log('🔍 [COURIER API] Fetching recipient, listing, and granter data...');
            const [recipientResponse, listingResponse, granterResponse] = await Promise.all([
                supabase
                    .from('profiles')
                    .select('user_id, first_name, last_name, email')
                    .eq('user_id', recipientUserId)
                    .single(),
                supabase
                    .from('listings')
                    .select('id, title, slug')
                    .eq('id', listingId)
                    .single(),
                supabase
                    .from('profiles')
                    .select('first_name, last_name, profile_photo')
                    .eq('user_id', session.user.id)
                    .single()
            ]);

            console.log('📊 [COURIER API] Database responses for data room access:', {
                recipient: recipientResponse.data,
                listing: listingResponse.data,
                granter: granterResponse.data,
                errors: {
                    recipient: recipientResponse.error,
                    listing: listingResponse.error,
                    granter: granterResponse.error
                }
            });

            if (recipientResponse.error || !recipientResponse.data) {
                console.log('❌ [COURIER API] Recipient not found for data room access:', recipientResponse.error);
                return NextResponse.json({ error: 'Recipient not found' }, { status: 404 });
            }

            if (listingResponse.error || !listingResponse.data) {
                console.log('❌ [COURIER API] Listing not found for data room access:', listingResponse.error);
                return NextResponse.json({ error: 'Listing not found' }, { status: 404 });
            }

            const recipient = recipientResponse.data;
            const listing = listingResponse.data;
            const granter = granterResponse.data;

            const granterName = granter 
                ? `${granter.first_name || ''} ${granter.last_name || ''}`.trim() || 'Unknown User'
                : 'Unknown User';

            console.log('📤 [COURIER API] Preparing to send data room access notification via Courier...');
            const accessNotificationData = {
                recipientUserId: recipient.user_id,
                recipientEmail: recipient.email,
                grantedByName: granterName,
                grantedByProfilePhoto: granter?.profile_photo,
                listingTitle: listing.title,
                listingId: listing.id,
                listingSlug: listing.slug,
            };
            console.log('📋 [COURIER API] Data room access notification data:', accessNotificationData);

            const result = await sendDataRoomAccessNotification(accessNotificationData);
            console.log('🎯 [COURIER API] Data room access Courier result:', result);

            return NextResponse.json(result);

        } else {
            return NextResponse.json({ error: 'Invalid notification type' }, { status: 400 });
        }

    } catch (error) {
        console.error('Error sending Courier notification:', error);
        return NextResponse.json({
            error: 'Internal Server Error',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 });
    }
} 