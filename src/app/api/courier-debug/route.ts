import { NextRequest, NextResponse } from 'next/server';
import { CourierClient } from '@trycourier/courier';

const authToken = process.env.COURIER_AUTH_TOKEN;
const courierClient = new CourierClient({
    authorizationToken: authToken,
});

export async function GET(request: NextRequest) {
    if (!authToken) {
        return NextResponse.json({ error: 'COURIER_AUTH_TOKEN not configured' }, { status: 500 });
    }

    const { searchParams } = new URL(request.url);
    const requestId = searchParams.get('requestId');
    const userId = searchParams.get('userId');

    try {
        const results: Record<string, unknown> = {};

        // If we have a request ID, get the message details
        if (requestId) {
            console.log('🔍 [COURIER DEBUG] Looking up message:', requestId);
            try {
                const messageDetails = await courierClient.messages.get(requestId);
                results.messageDetails = messageDetails;
                console.log('📋 [COURIER DEBUG] Message details:', messageDetails);
            } catch (error) {
                console.log('❌ [COURIER DEBUG] Error getting message details:', error);
                results.messageError = error instanceof Error ? error.message : 'Unknown error';
            }
        }

        // If we have a user ID, get user profile
        if (userId) {
            console.log('🔍 [COURIER DEBUG] Looking up user profile:', userId);
            try {
                const userProfile = await courierClient.profiles.get(userId);
                results.userProfile = userProfile;
                console.log('👤 [COURIER DEBUG] User profile:', userProfile);
            } catch (error) {
                console.log('❌ [COURIER DEBUG] Error getting user profile:', error);
                results.userError = error instanceof Error ? error.message : 'Unknown error';
            }
        }

        return NextResponse.json({
            success: true,
            authTokenConfigured: !!authToken,
            templateId: process.env.COURIER_MESSAGE_TEMPLATE_ID || 'message-notification',
            requestId,
            userId,
            ...results
        });

    } catch (error) {
        console.error('❌ [COURIER DEBUG] Error:', error);
        return NextResponse.json({
            error: error instanceof Error ? error.message : 'Unknown error',
            authTokenConfigured: !!authToken,
        }, { status: 500 });
    }
} 