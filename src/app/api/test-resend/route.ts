import { NextResponse } from 'next/server';

export async function POST() {
  try {
    const RESEND_API_KEY = process.env.RESEND_API_KEY;

    if (!RESEND_API_KEY) {
      return NextResponse.json(
        {
          success: false,
          error: 'RESEND_API_KEY environment variable is not set',
        },
        { status: 500 }
      );
    }

    console.log('🔑 Using API key:', RESEND_API_KEY.substring(0, 10) + '...');

    const emailPayload = {
      from: 'Test <<EMAIL>>',
      to: '<EMAIL>',
      subject: 'Test Email from Evermark',
      html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h1 style="color: #333;">✅ Resend Integration Test</h1>
                    <p>This is a test email to verify that <PERSON>sen<PERSON> is working correctly with your verified domain.</p>
                    <div style="background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <strong>Domain:</strong> updates.evermark.ai<br>
                        <strong>Time:</strong> ${new Date().toLocaleString()}<br>
                        <strong>Status:</strong> ✅ Successfully sent
                    </div>
                    <p>If you receive this email, the integration is working perfectly!</p>
                </div>
            `,
    };

    console.log('📧 Sending test email...');

    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailPayload),
    });

    console.log(`📬 Resend response status: ${response.status}`);

    if (!response.ok) {
      const errorData = await response.json();
      console.error('❌ Resend API error:', errorData);

      return NextResponse.json(
        {
          success: false,
          error: 'Failed to send email',
          details: errorData,
          status: response.status,
        },
        { status: 400 }
      );
    }

    const result = await response.json();
    console.log('✅ Email sent successfully:', result.id);

    return NextResponse.json({
      success: true,
      message: 'Test email sent successfully!',
      emailId: result.id,
      to: emailPayload.to,
      domain: 'updates.evermark.ai',
    });
  } catch (error) {
    console.error('❌ Error in test-resend endpoint:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
