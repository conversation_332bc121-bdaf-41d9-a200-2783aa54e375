'use client'

import { createClient } from '@/utils/supabase/client'
import Link from 'next/link'
import Image from 'next/image'
import { redirect } from 'next/navigation'
import { PriceFormatter } from '@/components/common/PriceFormatter'
import { Pencil, BookmarkPlus, Store, Eye, User as UserIcon, MessageSquare, Settings, Building, Heart, Shield } from 'lucide-react'
import { SavedListingItem } from './components/SavedListingItem'
import { NewestMatchCard } from './components/NewestMatchCard'
import { SavedListingProvider } from '@/contexts/SavedListingContext'
import UserRoleTabs from '@/components/profile/UserRoleTabs'
import { AccessControlProvider } from '@/contexts/AccessControlContext'
import { ViewModeProvider } from '@/contexts/ViewModeContext'
import { useState, useEffect, useCallback } from 'react'
import type { User } from '@supabase/auth-js'
import type { UserRole } from '@/types/supabase'

type AccountProfile = {
    user_id: string
    first_name: string | null
    last_name: string | null
    profile_photo: string | null
    company: string | null
    title: string | null
    user_role: UserRole | null
}

type MyListing = {
    id: string
    title: string
    price: number
    image_url: string | null
}

type SavedListing = {
    listing_id: string
    listings: {
        id: string
        title: string
        description: string
        price: number
        image_url: string | null
        listing_anonymized_details?: {
            anonymous_title?: string | null
            anonymous_image_url?: string | null
        } | null
    }
}

export default function AccountDashboard() {
    const [user, setUser] = useState<User | null>(null)
    const [profile, setProfile] = useState<AccountProfile | null>(null)
    const [myListings, setMyListings] = useState<MyListing[]>([])
    const [savedListings, setSavedListings] = useState<SavedListing[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [matchRefreshTrigger, setMatchRefreshTrigger] = useState(0)
    const supabase = createClient()

    const fetchAccountData = useCallback(async () => {
        try {
            const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser()

            if (userError || !currentUser) {
                redirect('/login')
                return
            }

            setUser(currentUser)

            // Fetch profile data
            const { data: profileData } = await supabase
                .from('profiles')
                .select('user_id, first_name, last_name, profile_photo, company, title, user_role')
                .eq('user_id', currentUser.id)
                .single()

            setProfile(profileData)

            // Fetch latest listings (limit to 3)
            const { data: listingsData } = await supabase
                .from('listings')
                .select('*')
                .eq('user_id', currentUser.id)
                .order('created_at', { ascending: false })
                .limit(3)

            setMyListings(listingsData || [])

            // Fetch saved listings (limit to 3)
            const { data: savedListingsData } = await supabase
                .from('saved_listings')
                .select(`
                    listing_id,
                    listings!inner (
                        id,
                        title,
                        description,
                        price,
                        image_url,
                        listing_anonymized_details (
                            anonymous_title,
                            anonymous_image_url
                        )
                    )
                `)
                .eq('user_id', currentUser.id)
                .limit(3)
                .returns<SavedListing[]>()

            setSavedListings(savedListingsData || [])
        } catch (error) {
            console.error('Error fetching account data:', error)
        } finally {
            setIsLoading(false)
        }
    }, [supabase])

    const refreshSavedListings = async () => {
        if (!user) return

        const { data: savedListingsData } = await supabase
            .from('saved_listings')
            .select(`
                listing_id,
                listings!inner (
                    id,
                    title,
                    description,
                    price,
                    image_url,
                    listing_anonymized_details (
                        anonymous_title,
                        anonymous_image_url
                    )
                )
            `)
            .eq('user_id', user.id)
            .limit(3)
            .returns<SavedListing[]>()

        setSavedListings(savedListingsData || [])
    }

    const handleMatchChange = () => {
        // Refresh saved listings when match changes (due to save/unsave)
        console.log('Match changed, refreshing saved listings')
        refreshSavedListings()
    }

    const handleSavedListingRemove = () => {
        console.log('Saved listing removed, refreshing both sections')
        // Refresh saved listings
        refreshSavedListings()
        // Force match card to refresh by incrementing trigger
        setMatchRefreshTrigger(prev => prev + 1)
    }

    const handleRoleChange = (newRole: UserRole) => {
        // Update local profile state
        setProfile(prev => prev ? { ...prev, user_role: newRole } : null)
        console.log('User role changed to:', newRole)
    }

    useEffect(() => {
        fetchAccountData()
    }, [fetchAccountData])

    if (isLoading) {
        return (
            <main className="min-h-screen py-8 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="animate-pulse">
                        <div className="h-32 bg-gray-200 rounded-xl mb-8"></div>
                        <div className="flex flex-col lg:flex-row gap-8">
                            <div className="lg:w-1/3">
                                <div className="h-96 bg-gray-200 rounded-xl"></div>
                            </div>
                            <div className="lg:w-2/3">
                                <div className="space-y-8">
                                    <div className="h-64 bg-gray-200 rounded-xl"></div>
                                    <div className="h-64 bg-gray-200 rounded-xl"></div>
                                    <div className="h-64 bg-gray-200 rounded-xl"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        )
    }

    if (!user) {
        redirect('/login')
        return null
    }

    return (
        <SavedListingProvider>
            <main className="min-h-screen py-8 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex flex-col lg:flex-row gap-8">
                        {/* Left Column - Profile Card */}
                        <div className="lg:w-1/3 space-y-6">
                            {/* Profile Information Card */}
                            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                                <div className="flex items-center space-x-3 mb-6">
                                    <div className="p-2 bg-blue-50 rounded-lg">
                                        <UserIcon className="w-5 h-5 text-blue-600" />
                                    </div>
                                    <h2 className="text-xl font-semibold text-gray-900">Profile</h2>
                                </div>

                                <div className="flex flex-col items-center text-center">
                                    <div className="relative mb-6">
                                        <Image
                                            src={profile?.profile_photo || '/images/default-avatar.png'}
                                            alt="Profile"
                                            width={120}
                                            height={120}
                                            className="rounded-full ring-4 ring-gray-100"
                                        />
                                    </div>

                                    <h1 className="text-2xl font-bold text-gray-900 mb-2">
                                        {profile?.first_name && profile?.last_name
                                            ? `${profile.first_name} ${profile.last_name}`
                                            : 'Your Name'
                                        }
                                    </h1>

                                    <div className="inline-block bg-gray-100 rounded-full px-4 py-2 text-sm text-gray-600 mb-4">
                                        {user.email}
                                    </div>

                                    {profile?.company && (
                                        <div className="flex items-center space-x-2 text-gray-600 mb-6">
                                            <Building className="w-4 h-4" />
                                            <span className="text-sm">
                                                {profile.title ? `${profile.title} at ` : ''}{profile.company}
                                            </span>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* User Role Tabs */}
                            <UserRoleTabs
                                currentRole={profile?.user_role || 'seller_buyer'}
                                onRoleChange={handleRoleChange}
                            />

                            {/* Quick Actions Card */}
                            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                                <div className="flex items-center space-x-3 mb-6">
                                    <div className="p-2 bg-purple-50 rounded-lg">
                                        <Settings className="w-5 h-5 text-purple-600" />
                                    </div>
                                    <h2 className="text-xl font-semibold text-gray-900">Quick Actions</h2>
                                </div>

                                <div className="space-y-3">
                                    <Link
                                        href="/account/inbox"
                                        className="flex items-center justify-center w-full bg-neutral-800 text-white px-6 py-3 rounded-lg hover:bg-neutral-700 transition-colors group"
                                    >
                                        <MessageSquare className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                                        Go to Messages
                                    </Link>

                                    <Link
                                        href="/account/edit"
                                        className="flex items-center justify-center w-full border border-gray-300 bg-white text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-colors group"
                                    >
                                        <Pencil className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                                        Edit Profile
                                    </Link>

                                    <Link
                                        href="/account/security"
                                        className="flex items-center justify-center w-full border border-gray-300 bg-white text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-colors group"
                                    >
                                        <Shield className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                                        Security Settings
                                    </Link>
                                </div>
                            </div>
                        </div>

                        {/* Right Column - Listings */}
                        <div className="lg:w-2/3 space-y-8">
                            {/* My Listings Card - Only show for seller and seller_buyer */}
                            {(profile?.user_role === 'seller' || profile?.user_role === 'seller_buyer') && (
                                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                                    <div className="flex items-center space-x-3 mb-6">
                                        <div className="p-2 bg-green-50 rounded-lg">
                                            <Store className="w-5 h-5 text-green-600" />
                                        </div>
                                        <h2 className="text-xl font-semibold text-gray-900">My Listings</h2>
                                        <div className="flex-1"></div>
                                        <Link
                                            href="/account/my-listings"
                                            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm hover:bg-gray-200 transition-colors font-medium"
                                        >
                                            View All
                                        </Link>
                                    </div>

                                    {myListings && myListings.length > 0 ? (
                                        <div className="space-y-4">
                                            {myListings.map((listing) => (
                                                <div
                                                    key={listing.id}
                                                    className="flex flex-col sm:flex-row sm:items-center gap-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-colors"
                                                >
                                                    <div className="flex items-center gap-4 flex-grow">
                                                        <div className="flex-shrink-0 w-20 h-20 relative rounded-lg overflow-hidden ring-1 ring-gray-200">
                                                            <Image
                                                                src={listing.image_url || '/images/placeholder.png'}
                                                                alt={listing.title}
                                                                fill
                                                                className="object-cover"
                                                            />
                                                        </div>
                                                        <div className="flex-grow">
                                                            <h3 className="font-semibold text-gray-900">{listing.title}</h3>
                                                            <PriceFormatter price={listing.price} className="text-sm text-gray-600 mt-1" />
                                                        </div>
                                                    </div>
                                                    <div className="flex gap-2 w-full sm:w-auto">
                                                        <Link
                                                            href={`/listings/${listing.id}`}
                                                            className="flex items-center justify-center flex-1 sm:flex-initial px-3 py-2 bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100 hover:border-gray-300 rounded-lg transition-colors text-sm font-medium group"
                                                            title="View Listing"
                                                        >
                                                            <Eye className="w-4 h-4 mr-1.5 group-hover:scale-110 transition-transform" />
                                                            View
                                                        </Link>
                                                        <Link
                                                            href={`/account/my-listings/edit/${listing.id}`}
                                                            className="flex items-center justify-center flex-1 sm:flex-initial px-3 py-2 bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100 hover:border-gray-300 rounded-lg transition-colors text-sm font-medium group"
                                                            title="Edit Listing"
                                                        >
                                                            <Pencil className="w-4 h-4 mr-1.5 group-hover:scale-110 transition-transform" />
                                                            Edit
                                                        </Link>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8 bg-gray-50/50 rounded-lg border border-gray-200/50">
                                            <Store className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                                            <h3 className="text-lg font-medium text-gray-900 mb-1">No listings yet</h3>
                                            <p className="text-gray-600 text-sm">Start selling by creating your first listing</p>
                                        </div>
                                    )}
                                </div>
                            )}

                            {/* Newest Match Card - Only show for buyer and seller_buyer */}
                            {(profile?.user_role === 'buyer' || profile?.user_role === 'seller_buyer') && (
                                <NewestMatchCard
                                    userId={user.id}
                                    onMatchChange={handleMatchChange}
                                    refreshTrigger={matchRefreshTrigger}
                                />
                            )}

                            {/* Saved Listings Card - Only show for buyer and seller_buyer */}
                            {(profile?.user_role === 'buyer' || profile?.user_role === 'seller_buyer') && (
                                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                                    <div className="flex items-center space-x-3 mb-6">
                                        <div className="p-2 bg-red-50 rounded-lg">
                                            <Heart className="w-5 h-5 text-red-600" />
                                        </div>
                                        <h2 className="text-xl font-semibold text-gray-900">Saved Listings</h2>
                                        <div className="flex-1"></div>
                                        <Link
                                            href="/account/saved-listings"
                                            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm hover:bg-gray-200 transition-colors font-medium"
                                        >
                                            View All
                                        </Link>
                                    </div>

                                    {savedListings && savedListings.length > 0 ? (
                                        <div className="space-y-4">
                                            {savedListings.map((saved) => (
                                                <AccessControlProvider key={saved.listing_id} listingId={saved.listing_id} userId={user.id}>
                                                    <ViewModeProvider initialIsPublic={false}>
                                                        <SavedListingItem
                                                            listing={saved}
                                                            onRemove={handleSavedListingRemove}
                                                        />
                                                    </ViewModeProvider>
                                                </AccessControlProvider>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8 bg-gray-50/50 rounded-lg border border-gray-200/50">
                                            <BookmarkPlus className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                                            <h3 className="text-lg font-medium text-gray-900 mb-1">No saved listings</h3>
                                            <p className="text-gray-600 text-sm">Save listings you&apos;re interested in to view them here</p>
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </main>
        </SavedListingProvider>
    )
} 