'use client';

import Link from 'next/link'
import { PriceFormatter } from '@/components'
import { BookmarkPlus, Heart, Search, User, ChevronRight } from 'lucide-react'
import { AccessControlProvider } from '@/contexts/AccessControlContext'
import { ViewModeProvider } from '@/contexts/ViewModeContext'
import { AccessControlledText, AccessControlledImage } from '@/components/AccessControlled'

type SavedListing = {
    listing_id: string;
    listings: {
        id: string;
        title: string;
        description: string;
        price: number;
        image_url: string | null;
        created_at: string;
        listing_anonymized_details?: {
            anonymous_title?: string | null;
            anonymous_description?: string | null;
            anonymous_image_url?: string | null;
        } | null;
    };
}

interface SavedListingsClientProps {
    savedListings: SavedListing[];
    userId: string;
}

function SavedListingCard({ saved, userId }: { saved: SavedListing; userId: string }) {
    const anonymizedDetails = Array.isArray(saved.listings.listing_anonymized_details) 
        ? saved.listings.listing_anonymized_details[0] 
        : saved.listings.listing_anonymized_details;

    return (
        <AccessControlProvider listingId={saved.listing_id} userId={userId}>
            <ViewModeProvider initialIsPublic={false}>
                <Link
                    href={`/listings/${saved.listing_id}`}
                    className="bg-white rounded-xl shadow-sm border border-gray-200/60 overflow-hidden hover:shadow-md transition-shadow group block"
                >
                    {/* Image Section */}
                    <div className="relative h-48">
                        <AccessControlledImage
                            realImageUrl={saved.listings.image_url}
                            anonymousImageUrl={anonymizedDetails?.anonymous_image_url}
                            alt={saved.listings.title}
                            fallbackImageUrl="/images/placeholder-listing-image.jpg"
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-200"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
                        <div className="absolute top-3 right-3">
                            <div className="p-2 bg-red-50/90 backdrop-blur-sm rounded-full">
                                <Heart className="w-4 h-4 text-red-600 fill-current" />
                            </div>
                        </div>
                    </div>

                    {/* Content Section */}
                    <div className="p-6">
                        <AccessControlledText
                            realText={saved.listings.title}
                            anonymousText={anonymizedDetails?.anonymous_title}
                            fallback="Untitled Listing"
                            as="h2"
                            className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors"
                        />
                        <AccessControlledText
                            realText={saved.listings.description}
                            anonymousText={anonymizedDetails?.anonymous_description}
                            fallback="No description available"
                            as="p"
                            className="text-gray-600 text-sm mb-4 line-clamp-3"
                        />
                        <div className="flex items-center justify-between">
                            <PriceFormatter
                                price={saved.listings.price}
                                className="text-lg font-semibold text-gray-900"
                            />
                            <span className="text-xs text-gray-500">
                                Saved {new Date(saved.listings.created_at).toLocaleDateString()}
                            </span>
                        </div>
                    </div>
                </Link>
            </ViewModeProvider>
        </AccessControlProvider>
    );
}

export default function SavedListingsClient({ savedListings, userId }: SavedListingsClientProps) {
    return (
        <main className="min-h-screen py-8 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Breadcrumb Navigation */}
                <div className="mb-8">
                    <nav className="flex items-center space-x-2 text-sm">
                        <Link
                            href="/account"
                            className="flex items-center space-x-1 text-gray-600 hover:text-gray-900 transition-colors"
                        >
                            <User className="w-4 h-4" />
                            <span>Account</span>
                        </Link>
                        <ChevronRight className="w-4 h-4 text-gray-400" />
                        <div className="flex items-center space-x-1 text-gray-900">
                            <Heart className="w-4 h-4" />
                            <span>Saved Listings</span>
                        </div>
                    </nav>
                </div>

                {/* Header Card */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-6 sm:p-8 mb-8">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div className="flex items-center space-x-3">
                            <div className="p-2 bg-red-50 rounded-lg">
                                <Heart className="w-5 h-5 text-red-600" />
                            </div>
                            <div>
                                <h1 className="text-2xl font-semibold text-gray-900">Saved Businesses</h1>
                                <p className="text-gray-600 text-sm mt-1">
                                    {savedListings.length} {savedListings.length === 1 ? 'business' : 'businesses'} saved
                                </p>
                            </div>
                        </div>
                        <Link
                            href="/listings"
                            className="inline-flex items-center justify-center w-full sm:w-auto px-4 py-2 bg-neutral-800 text-white rounded-lg hover:bg-neutral-700 transition-colors font-medium group"
                        >
                            <Search className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                            Browse More
                        </Link>
                    </div>
                </div>

                {savedListings.length === 0 ? (
                    /* Empty State Card */
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-12">
                        <div className="flex flex-col items-center max-w-md mx-auto text-center">
                            <div className="p-4 bg-red-50 rounded-full mb-6">
                                <BookmarkPlus className="w-12 h-12 text-red-600" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-3">No Saved Businesses Yet</h3>
                            <p className="text-gray-600 mb-8 leading-relaxed">
                                Start saving interesting businesses you&apos;d like to track.
                                Browse our marketplace to find your next opportunity.
                            </p>
                            <Link
                                href="/listings"
                                className="inline-flex items-center px-6 py-3 bg-neutral-800 text-white rounded-lg hover:bg-neutral-700 transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-500 font-medium group"
                            >
                                <Search className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                                Browse Businesses
                            </Link>
                        </div>
                    </div>
                ) : (
                    /* Listings Grid */
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {savedListings.map((saved) => (
                            <SavedListingCard 
                                key={saved.listing_id} 
                                saved={saved} 
                                userId={userId}
                            />
                        ))}
                    </div>
                )}
            </div>
        </main>
    )
} 