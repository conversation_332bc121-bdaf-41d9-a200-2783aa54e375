import { Metadata } from 'next'
import { createClient } from '@/utils/supabase/server'
import { redirect } from 'next/navigation'
import SavedListingsClient from './SavedListingsClient'

export const metadata: Metadata = {
    title: 'Saved Businesses | Business Marketplace',
    description: 'View your saved businesses',
}

type SavedListing = {
    listing_id: string;
    listings: {
        id: string;
        title: string;
        description: string;
        price: number;
        image_url: string | null;
        created_at: string;
        listing_anonymized_details?: {
            anonymous_title?: string | null;
            anonymous_description?: string | null;
            anonymous_image_url?: string | null;
        } | null;
    };
}

export default async function SavedListingsPage() {
    const supabase = await createClient()

    const { data: { session } } = await supabase.auth.getSession()

    if (!session?.user) {
        redirect('/login')
    }

    const { data: savedListings, error } = await supabase
        .from('saved_listings')
        .select(`
            listing_id,
            listings (
                id,
                title,
                description,
                price,
                image_url,
                created_at,
                listing_anonymized_details (
                    anonymous_title,
                    anonymous_description,
                    anonymous_image_url
                )
            )
        `)
        .eq('user_id', session.user.id)
        .returns<SavedListing[]>();

    if (error) {
        console.error('Error fetching saved listings:', error)
        return <div>Error loading saved listings</div>
    }

    return <SavedListingsClient savedListings={savedListings} userId={session.user.id} />
} 