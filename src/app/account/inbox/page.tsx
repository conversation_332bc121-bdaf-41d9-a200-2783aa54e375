import { createClient } from '@/utils/supabase/server'
import { redirect } from 'next/navigation'
import Link from 'next/link'
import { User, MessageSquare, ChevronRight } from 'lucide-react'
import InboxContent from '../../messages/components/InboxContent'

export default async function InboxPage() {
    const supabase = await createClient()

    const { data: { session } } = await supabase.auth.getSession()

    if (!session?.user) {
        redirect('/login')
    }

    return (
        <main className="min-h-screen md:py-8 py-2 bg-gray-50">
            <div className="max-w-7xl mx-auto md:px-4 sm:px-6 lg:px-8 px-2">
                {/* Breadcrumb Navigation */}
                <div className="hidden md:block mb-8">
                    <nav className="flex items-center space-x-2 text-sm">
                        <Link
                            href="/account"
                            className="flex items-center space-x-1 text-gray-600 hover:text-gray-900 transition-colors"
                        >
                            <User className="w-4 h-4" />
                            <span>Account</span>
                        </Link>
                        <ChevronRight className="w-4 h-4 text-gray-400" />
                        <div className="flex items-center space-x-1 text-gray-900">
                            <MessageSquare className="w-4 h-4" />
                            <span>Messages</span>
                        </div>
                    </nav>
                </div>

                {/* Page Header - Hidden on mobile */}
                <div className="hidden md:block bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                    <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-50 rounded-lg">
                            <MessageSquare className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">Messages</h1>
                            <p className="mt-2 text-gray-600">
                                Communicate with potential buyers and sellers about your listings.
                            </p>
                        </div>
                    </div>
                </div>

                {/* Inbox Content */}
                <div className="bg-white md:rounded-xl rounded-lg shadow-sm border border-gray-200/60 overflow-hidden" style={{ height: 'calc(100vh - 120px)', minHeight: '600px' }}>
                    <InboxContent />
                </div>
            </div>
        </main>
    );
}