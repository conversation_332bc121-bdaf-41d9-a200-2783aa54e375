'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/utils/supabase/client'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import Image from 'next/image'
import { useViewMode } from '@/contexts/ViewModeContext'
import ViewModeToggle from '@/app/listings/components/ViewModeToggle'
import {
    FileText,
    Building2,
    Camera,
    Save,
    X,
    Loader2,
    Info,
    MapPin,
    Users,
    TrendingUp,
    ChevronDown,
    ChevronUp,
    Calendar,
    Percent,
    Hash,
    Globe,
    Building,
    DollarSign,
    MessageSquare,
    Sparkles,
    Store
} from 'lucide-react'

// Import the range slider components
import RevenueRangeSlider from '@/components/forms/RevenueRangeSlider'
import ProfitRangeSlider from '@/components/forms/ProfitRangeSlider'
import LastMonthRevenueSlider from '@/components/forms/LastMonthRevenueSlider'
import LastMonthProfitSlider from '@/components/forms/LastMonthProfitSlider'
import RecurringRevenueSlider from '@/components/forms/RecurringRevenueSlider'

interface Listing {
    id: string
    title: string
    description: string
    price: number
    industry_id: string
    sub_industry_id: string | null
    image_url: string
    website?: string
    status?: 'live' | 'draft'
    industries?: {
        id: string
        name: string
    }
    sub_industries?: {
        id: string
        name: string
    }
}

interface ListingDetails {
    year_established?: number | null
    legal_structure?: string | null
    team_size?: number | null
    active_customers?: number | null
    growth_rate?: string | null
    reason_for_selling?: string | null
    state_id?: string | null
    city?: string | null
    street_address?: string | null
    postal_code?: string | null
    latitude?: number | null
    longitude?: number | null
    naics_code?: string | null
    annual_revenue_ttm_min?: number | null
    annual_revenue_ttm_max?: number | null
    annual_net_profit_ttm_min?: number | null
    annual_net_profit_ttm_max?: number | null
    last_month_revenue_min?: number | null
    last_month_revenue_max?: number | null
    last_month_profit_min?: number | null
    last_month_profit_max?: number | null
    recurring_revenue_min?: number | null
    recurring_revenue_max?: number | null
}

interface AnonymizedDetails {
    id?: string
    listing_id?: string
    anonymous_title: string
    anonymous_description: string | null
    anonymous_image_url: string | null
    created_at?: string
}

interface Props {
    initialListing: Listing
    initialAnonymizedDetails?: AnonymizedDetails | null
}

export default function EditListingForm({ initialListing, initialAnonymizedDetails }: Props) {
    const [listing, setListing] = useState(initialListing)
    const [listingDetails, setListingDetails] = useState<ListingDetails>({})
    const [anonymizedDetails, setAnonymizedDetails] = useState<AnonymizedDetails>({
        anonymous_title: initialAnonymizedDetails?.anonymous_title || '',
        anonymous_description: initialAnonymizedDetails?.anonymous_description || null,
        anonymous_image_url: initialAnonymizedDetails?.anonymous_image_url || null
    })
    const [isLoading, setIsLoading] = useState(false)
    const [detailsLoading, setDetailsLoading] = useState(true)
    const [selectedImage, setSelectedImage] = useState<File | null>(null)
    const [imagePreview, setImagePreview] = useState<string | null>(null)
    const [industries, setIndustries] = useState<{ id: string, name: string, naics_code?: string | null }[]>([])
    const [subIndustries, setSubIndustries] = useState<{ id: string, name: string, naics_code?: string | null }[]>([])
    const [states, setStates] = useState<{ id: string, name: string, code: string }[]>([])
    const [customReason, setCustomReason] = useState<string>('')
    const [expandedSections, setExpandedSections] = useState({
        businessDetails: true,
        financialDetails: true,
        locationDetails: true,
        additionalInfo: true
    })
    const [regenerateAnonymized, setRegenerateAnonymized] = useState(false)
    const [selectedAnonymizedImage, setSelectedAnonymizedImage] = useState<File | null>(null)
    const [anonymizedImagePreview, setAnonymizedImagePreview] = useState<string | null>(null)
    const [aiGeneratedImageUrls, setAiGeneratedImageUrls] = useState<string[]>([])
    const [isGeneratingImages, setIsGeneratingImages] = useState(false)
    const router = useRouter()
    const supabase = createClient()
    const { isPublicView } = useViewMode()

    // Add validation error state
    const [validationErrors, setValidationErrors] = useState<{
        anonymous_title?: string
        anonymous_description?: string
        title?: string
        description?: string
        price?: string
        industry_id?: string
        customReason?: string
    }>({})

    // Fetch all data on component mount
    useEffect(() => {
        const fetchData = async () => {
            try {
                // Fetch industries
                const { data: industriesData } = await supabase
                    .from('industries')
                    .select('id, name, naics_code')
                    .order('name')

                if (industriesData) {
                    setIndustries(industriesData)
                }

                // Fetch states
                const { data: statesData } = await supabase
                    .from('states')
                    .select('*')
                    .order('name')

                if (statesData) {
                    setStates(statesData)
                }

                // Fetch listing details
                const { data: detailsData } = await supabase
                    .from('listing_details')
                    .select('*')
                    .eq('listing_id', initialListing.id)
                    .single()

                if (detailsData) {
                    setListingDetails(detailsData)

                    // Check if reason_for_selling is a custom reason (not one of the predefined options)
                    const predefinedReasons = ['Retirement', 'New Opportunity', 'Health Reasons', 'Relocation', 'Partnership Dissolution', 'Financial Reasons', 'Other']
                    if (detailsData.reason_for_selling && !predefinedReasons.includes(detailsData.reason_for_selling)) {
                        // It's a custom reason, so set it to "Other" in the dropdown and populate the custom field
                        setListingDetails(prev => ({
                            ...prev,
                            reason_for_selling: 'Other'
                        }))
                        setCustomReason(detailsData.reason_for_selling)
                    }
                }
            } catch (error) {
                console.error('Error fetching data:', error)
            } finally {
                setDetailsLoading(false)
            }
        }

        fetchData()
    }, [supabase, initialListing.id])

    // Fetch sub-industries when industry changes
    useEffect(() => {
        const fetchSubIndustries = async () => {
            if (!listing.industry_id) return

            const { data } = await supabase
                .from('sub_industries')
                .select('id, name, naics_code')
                .eq('industry_id', listing.industry_id)
                .order('name')

            if (data) {
                setSubIndustries(data)
            }
        }

        fetchSubIndustries()
    }, [listing.industry_id, supabase])

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsLoading(true)

        // Comprehensive validation for all required fields
        const errors: {
            anonymous_title?: string
            anonymous_description?: string
            title?: string
            description?: string
            price?: string
            industry_id?: string
            customReason?: string
        } = {}

        // Always validate anonymized fields (required for public display)
        if (!anonymizedDetails.anonymous_title?.trim()) {
            errors.anonymous_title = 'Anonymous title is required for public display'
        }
        
        if (!anonymizedDetails.anonymous_description?.trim()) {
            errors.anonymous_description = 'Anonymous description is required for public display'
        }

        // Always validate private fields (required for private display)
        if (!listing.title?.trim()) {
            errors.title = 'Business title is required'
        }
        
        if (!listing.description?.trim()) {
            errors.description = 'Business description is required'
        }

        // Validate price (must be a positive number)
        if (!listing.price || listing.price <= 0) {
            errors.price = 'Asking price is required and must be greater than $0'
        }

        // Validate industry selection
        if (!listing.industry_id?.trim()) {
            errors.industry_id = 'Industry selection is required'
        }

        // Validate custom reason when "Other" is selected
        if (listingDetails.reason_for_selling === 'Other' && !customReason?.trim()) {
            errors.customReason = 'Please specify your reason for selling'
        }

        // If there are validation errors, show appropriate message based on current view
        if (Object.keys(errors).length > 0) {
            setValidationErrors(errors)
            setIsLoading(false)
            
            if (isPublicView) {
                // In Public View, show error about Public View fields
                const publicErrors = Object.keys(errors).filter(key => key.startsWith('anonymous_'))
                if (publicErrors.length > 0) {
                    toast.error('Please fill in all required fields in Public View')
                } else {
                    toast.error('Please complete the Private View before saving. Switch to Private View to fill in missing business details.')
                }
            } else {
                // In Private View, check what type of errors we have
                const privateErrors = Object.keys(errors).filter(key => !key.startsWith('anonymous_'))
                const publicErrors = Object.keys(errors).filter(key => key.startsWith('anonymous_'))
                
                if (privateErrors.length > 0 && publicErrors.length > 0) {
                    toast.error('Please fill in all required fields in both Private and Public views before saving')
                } else if (privateErrors.length > 0) {
                    toast.error('Please fill in all required fields in Private View')
                } else if (publicErrors.length > 0) {
                    toast.error('Please complete the Public View before saving. Switch to Public View to fill in missing anonymized details.')
                }
            }
            return
        }
        
        // Clear any previous validation errors
        setValidationErrors({})

        try {
            let image_url = listing.image_url
            let anonymized_image_url = anonymizedDetails.anonymous_image_url

            // Handle main image upload if a new image was selected
            if (selectedImage) {
                const fileName = `${Date.now()}-${selectedImage.name.replace(/[^a-zA-Z0-9]/g, '-')}`

                const { error: uploadError } = await supabase.storage
                    .from('listing-images')
                    .upload(fileName, selectedImage)

                if (uploadError) throw uploadError

                const { data: { publicUrl } } = supabase.storage
                    .from('listing-images')
                    .getPublicUrl(fileName)

                image_url = publicUrl
            }

            // Handle anonymized image upload if a new one was selected
            if (selectedAnonymizedImage) {
                const fileName = `anon-${Date.now()}-${selectedAnonymizedImage.name.replace(/[^a-zA-Z0-9]/g, '-')}`

                const { error: uploadError } = await supabase.storage
                    .from('listing-images')
                    .upload(fileName, selectedAnonymizedImage)

                if (uploadError) throw uploadError

                const { data: { publicUrl } } = supabase.storage
                    .from('listing-images')
                    .getPublicUrl(fileName)

                anonymized_image_url = publicUrl
            }

            // Update the main listing
            const { error: listingError } = await supabase
                .from('listings')
                .update({
                    title: listing.title,
                    description: listing.description,
                    price: listing.price,
                    industry_id: listing.industry_id,
                    sub_industry_id: listing.sub_industry_id,
                    website: listing.website,
                    image_url: image_url,
                    status: listing.status || 'live',
                })
                .eq('id', listing.id)

            if (listingError) throw listingError

            // Find the appropriate NAICS code
            let naicsCodeToSave: string | null = null
            if (listing.sub_industry_id) {
                const selectedSubIndustry = subIndustries.find(sub => sub.id === listing.sub_industry_id)
                if (selectedSubIndustry) {
                    naicsCodeToSave = selectedSubIndustry.naics_code || null
                }
            } else if (listing.industry_id) {
                const selectedIndustry = industries.find(ind => ind.id === listing.industry_id)
                if (selectedIndustry) {
                    naicsCodeToSave = selectedIndustry.naics_code || null
                }
            }

            // Update or insert listing details
            const detailsData = {
                listing_id: listing.id,
                year_established: listingDetails.year_established || null,
                legal_structure: listingDetails.legal_structure || null,
                team_size: listingDetails.team_size || null,
                active_customers: listingDetails.active_customers || null,
                growth_rate: listingDetails.growth_rate || null,
                reason_for_selling: listingDetails.reason_for_selling === 'Other'
                    ? (customReason.trim() || 'Other')
                    : (listingDetails.reason_for_selling || null),
                state_id: listingDetails.state_id || null,
                city: listingDetails.city || null,
                street_address: listingDetails.street_address || null,
                postal_code: listingDetails.postal_code || null,
                latitude: listingDetails.latitude || null,
                longitude: listingDetails.longitude || null,
                naics_code: naicsCodeToSave,
                annual_revenue_ttm_min: listingDetails.annual_revenue_ttm_min || null,
                annual_revenue_ttm_max: listingDetails.annual_revenue_ttm_max || null,
                annual_net_profit_ttm_min: listingDetails.annual_net_profit_ttm_min || null,
                annual_net_profit_ttm_max: listingDetails.annual_net_profit_ttm_max || null,
                last_month_revenue_min: listingDetails.last_month_revenue_min || null,
                last_month_revenue_max: listingDetails.last_month_revenue_max || null,
                last_month_profit_min: listingDetails.last_month_profit_min || null,
                last_month_profit_max: listingDetails.last_month_profit_max || null,
                recurring_revenue_min: listingDetails.recurring_revenue_min || null,
                recurring_revenue_max: listingDetails.recurring_revenue_max || null,
            }

            // Try to update first, if no rows affected, then insert
            const { error: updateError } = await supabase
                .from('listing_details')
                .update(detailsData)
                .eq('listing_id', listing.id)
                .select('*')

            if (updateError) {
                // If update failed, try to insert
                const { error: insertError } = await supabase
                    .from('listing_details')
                    .insert([detailsData])

                if (insertError) throw insertError
            }

            // Handle anonymized details
            const finalAnonymizedDetails = { ...anonymizedDetails }

            if (regenerateAnonymized) {
                // Generate new anonymized description based on current description
                try {
                    const response = await fetch('/api/generate-description', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            originalDescription: listing.description,
                            businessType: 'anonymized'
                        }),
                    })

                    if (response.ok) {
                        const data = await response.json()
                        if (data.anonymizedDescription) {
                            finalAnonymizedDetails.anonymous_description = data.anonymizedDescription
                        }
                    }
                } catch (error) {
                    console.error('Failed to regenerate description:', error)
                    // Continue with existing description
                }

                // Generate new anonymized title if industry changed
                const stateName = states.find(s => s.id === listingDetails.state_id)?.name || 'Undisclosed Location'
                const industryName = industries.find(i => i.id === listing.industry_id)?.name || 'Business'
                finalAnonymizedDetails.anonymous_title = `${industryName} in ${stateName}`
            }

            // Update anonymized image URL
            finalAnonymizedDetails.anonymous_image_url = anonymized_image_url

            // Update or insert anonymized details
            const anonymizedData = {
                listing_id: listing.id,
                anonymous_title: finalAnonymizedDetails.anonymous_title,
                anonymous_description: finalAnonymizedDetails.anonymous_description,
                anonymous_image_url: finalAnonymizedDetails.anonymous_image_url,
            }

            const { error: anonymizedUpdateError } = await supabase
                .from('listing_anonymized_details')
                .update(anonymizedData)
                .eq('listing_id', listing.id)
                .select('*')

            if (anonymizedUpdateError) {
                // If update failed, try to insert
                const { error: anonymizedInsertError } = await supabase
                    .from('listing_anonymized_details')
                    .insert([anonymizedData])

                if (anonymizedInsertError) throw anonymizedInsertError
            }

            toast.success('Listing updated successfully')
            router.push('/account/my-listings')
            router.refresh()
        } catch (error) {
            console.error('Error updating listing:', error)
            toast.error('Failed to update listing')
        } finally {
            setIsLoading(false)
        }
    }

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0]
            setSelectedImage(file)

            // Create preview URL
            const reader = new FileReader()
            reader.onload = (e) => {
                setImagePreview(e.target?.result as string)
            }
            reader.readAsDataURL(file)
        }
    }

    const clearImageSelection = () => {
        setSelectedImage(null)
        setImagePreview(null)
        // Reset the file input
        const fileInput = document.getElementById('image') as HTMLInputElement
        if (fileInput) fileInput.value = ''
    }

    const handleAnonymizedImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0]
            setSelectedAnonymizedImage(file)

            // Create preview URL
            const reader = new FileReader()
            reader.onload = (e) => {
                setAnonymizedImagePreview(e.target?.result as string)
            }
            reader.readAsDataURL(file)
        }
    }

    const clearAnonymizedImageSelection = () => {
        setSelectedAnonymizedImage(null)
        setAnonymizedImagePreview(null)
        // Reset the file input
        const fileInput = document.getElementById('anonymizedImage') as HTMLInputElement
        if (fileInput) fileInput.value = ''
    }

    const handleGenerateLummiImages = async () => {
        if (!listing.description) {
            toast.error('Please add a description first to generate images')
            return
        }

        setIsGeneratingImages(true)
        try {
            const response = await fetch('/api/generate-image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    description: listing.description,
                    count: 6
                }),
            })

            const data = await response.json()

            if (!response.ok) {
                throw new Error(data.error || 'Failed to generate images')
            }

            if (data.imageUrls && Array.isArray(data.imageUrls)) {
                setAiGeneratedImageUrls(data.imageUrls)
                toast.success('AI images generated successfully!')
            } else {
                throw new Error('No images returned from the API')
            }

        } catch (error) {
            console.error('Error generating images:', error)
            toast.error(error instanceof Error ? error.message : 'Failed to generate images')
        } finally {
            setIsGeneratingImages(false)
        }
    }

    const handleSelectAiImage = (imageUrl: string) => {
        setAnonymizedDetails(prev => ({
            ...prev,
            anonymous_image_url: imageUrl
        }))
        setAnonymizedImagePreview(null)
        setSelectedAnonymizedImage(null)
    }

    const toggleSection = (section: keyof typeof expandedSections) => {
        setExpandedSections(prev => ({
            ...prev,
            [section]: !prev[section]
        }))
    }

    // Financial range handlers
    const handleRevenueChange = useCallback((range: { min: number; max: number }) => {
        setListingDetails(prev => ({
            ...prev,
            annual_revenue_ttm_min: range.min,
            annual_revenue_ttm_max: range.max
        }))
    }, [])

    const handleProfitChange = useCallback((range: { min: number; max: number }) => {
        setListingDetails(prev => ({
            ...prev,
            annual_net_profit_ttm_min: range.min,
            annual_net_profit_ttm_max: range.max
        }))
    }, [])

    const handleLastMonthRevenueChange = useCallback((range: { min: number; max: number }) => {
        setListingDetails(prev => ({
            ...prev,
            last_month_revenue_min: range.min,
            last_month_revenue_max: range.max
        }))
    }, [])

    const handleLastMonthProfitChange = useCallback((range: { min: number; max: number }) => {
        setListingDetails(prev => ({
            ...prev,
            last_month_profit_min: range.min,
            last_month_profit_max: range.max
        }))
    }, [])

    const handleRecurringRevenueChange = useCallback((range: { min: number; max: number }) => {
        setListingDetails(prev => ({
            ...prev,
            recurring_revenue_min: range.min,
            recurring_revenue_max: range.max
        }))
    }, [])

    if (detailsLoading) {
        return (
            <div className="flex items-center justify-center py-12">
                <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
                <span className="ml-2 text-gray-600">Loading listing details...</span>
            </div>
        )
    }

    return (
        <div className="space-y-8">
            {/* View Mode Toggle */}
            <ViewModeToggle />

            {/* Conditional content based on view mode */}
            {isPublicView ? (
                // Buyer View - Edit Anonymized Details
                <div className="space-y-8">
                    <div className="space-y-6">
                        <div className="flex items-center space-x-3 mb-6">
                            <div className="p-2 bg-blue-50 rounded-lg">
                                <FileText className="w-5 h-5 text-blue-600" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Public View - Anonymized Information</h2>
                                <p className="text-gray-600 text-sm mt-1">
                                    Edit how potential buyers see your listing before approval
                                </p>
                            </div>
                        </div>

                        <div className="grid grid-cols-1 gap-6">
                            <div>
                                <label htmlFor="anonymous_title" className="block text-sm font-medium text-gray-700 mb-2">
                                    Anonymous Business Title *
                                </label>
                                <input
                                    type="text"
                                    id="anonymous_title"
                                    value={anonymizedDetails.anonymous_title}
                                    onChange={(e) => {
                                        setAnonymizedDetails({ ...anonymizedDetails, anonymous_title: e.target.value })
                                        // Clear validation error when user starts typing
                                        if (validationErrors.anonymous_title) {
                                            setValidationErrors({ ...validationErrors, anonymous_title: undefined })
                                        }
                                    }}
                                    className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:border-transparent transition-colors ${
                                        validationErrors.anonymous_title 
                                            ? 'border-red-300 focus:ring-red-500' 
                                            : 'border-gray-300 focus:ring-blue-500'
                                    }`}
                                    placeholder="Enter anonymous title"
                                />
                                {validationErrors.anonymous_title && (
                                    <p className="text-sm text-red-600 mt-1">{validationErrors.anonymous_title}</p>
                                )}
                            </div>

                            <div>
                                <label htmlFor="anonymous_description" className="block text-sm font-medium text-gray-700 mb-2">
                                    Anonymous Description *
                                </label>
                                <textarea
                                    id="anonymous_description"
                                    value={anonymizedDetails.anonymous_description || ''}
                                    onChange={(e) => {
                                        setAnonymizedDetails({ ...anonymizedDetails, anonymous_description: e.target.value })
                                        // Clear validation error when user starts typing
                                        if (validationErrors.anonymous_description) {
                                            setValidationErrors({ ...validationErrors, anonymous_description: undefined })
                                        }
                                    }}
                                    rows={5}
                                    className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:border-transparent transition-colors resize-none ${
                                        validationErrors.anonymous_description 
                                            ? 'border-red-300 focus:ring-red-500' 
                                            : 'border-gray-300 focus:ring-blue-500'
                                    }`}
                                    placeholder="Provide an anonymous description of your business..."
                                />
                                {validationErrors.anonymous_description && (
                                    <p className="text-sm text-red-600 mt-1">{validationErrors.anonymous_description}</p>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Anonymized Image Management */}
                    <div className="border-t border-gray-200 pt-8">
                        <div className="flex items-center space-x-3 mb-6">
                            <div className="p-2 bg-purple-50 rounded-lg">
                                <Camera className="w-5 h-5 text-purple-600" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Anonymous Listing Image</h2>
                                <p className="text-gray-600 text-sm mt-1">
                                    Choose an image that represents your business without revealing its identity
                                </p>
                            </div>
                        </div>

                        <div className="space-y-6">
                            {/* Current Anonymous Image Display */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-3">
                                    Current Anonymous Image
                                </label>
                                <div className="relative w-full max-w-md h-48 rounded-lg overflow-hidden bg-gray-100">
                                    <Image
                                        src={anonymizedImagePreview || anonymizedDetails.anonymous_image_url || '/images/placeholder-listing-image.jpg'}
                                        alt="Anonymous listing image"
                                        fill
                                        className="object-cover"
                                    />
                                </div>
                            </div>

                            {/* AI Image Generation */}
                            <div>
                                <div className="flex items-center justify-between mb-4">
                                    <label className="block text-sm font-medium text-gray-700">
                                        Generate AI Images
                                    </label>
                                    <button
                                        type="button"
                                        onClick={handleGenerateLummiImages}
                                        disabled={isGeneratingImages || !listing.description}
                                        className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors font-medium disabled:bg-gray-400 disabled:cursor-not-allowed"
                                    >
                                        {isGeneratingImages ? (
                                            <>
                                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                                Generating...
                                            </>
                                        ) : (
                                            <>
                                                <Sparkles className="w-4 h-4 mr-2" />
                                                Generate Images
                                            </>
                                        )}
                                    </button>
                                </div>

                                {aiGeneratedImageUrls.length > 0 && (
                                    <div>
                                        <h4 className="text-sm font-medium mb-3">Select an AI-generated image:</h4>
                                        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                                            {aiGeneratedImageUrls.map((imageUrl, index) => (
                                                <div
                                                    key={index}
                                                    className={`relative cursor-pointer rounded-lg overflow-hidden h-24 border-2 hover:opacity-90 transition-all ${imageUrl === anonymizedDetails.anonymous_image_url ? 'border-purple-500 ring-2 ring-purple-300' : 'border-transparent'
                                                        }`}
                                                    onClick={() => handleSelectAiImage(imageUrl)}
                                                >
                                                    <Image
                                                        src={imageUrl}
                                                        alt={`AI Generated Image ${index + 1}`}
                                                        fill
                                                        className="object-cover"
                                                        sizes="100px"
                                                    />
                                                    {imageUrl === anonymizedDetails.anonymous_image_url && (
                                                        <div className="absolute bottom-1 right-1 bg-purple-500 rounded-full w-4 h-4 flex items-center justify-center">
                                                            <span className="text-white text-xs">✓</span>
                                                        </div>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Custom Anonymous Image Upload */}
                            <div>
                                <label htmlFor="anonymizedImage" className="block text-sm font-medium text-gray-700 mb-3">
                                    Upload Custom Anonymous Image
                                </label>
                                <div className="flex items-center space-x-4">
                                    <input
                                        type="file"
                                        id="anonymizedImage"
                                        accept="image/*"
                                        onChange={handleAnonymizedImageChange}
                                        className="block w-full text-sm text-gray-500
                                            file:mr-4 file:py-2 file:px-4
                                            file:rounded-lg file:border-0
                                            file:text-sm file:font-medium
                                            file:bg-purple-50 file:text-purple-700
                                            hover:file:bg-purple-100 file:cursor-pointer
                                            cursor-pointer border border-gray-300 rounded-lg p-2"
                                    />
                                    {selectedAnonymizedImage && (
                                        <button
                                            type="button"
                                            onClick={clearAnonymizedImageSelection}
                                            className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                                            title="Clear selection"
                                        >
                                            <X className="w-5 h-5" />
                                        </button>
                                    )}
                                </div>
                                {selectedAnonymizedImage && (
                                    <p className="text-sm text-green-600 mt-2 flex items-center">
                                        <Info className="w-4 h-4 mr-1" />
                                        New anonymous image selected: {selectedAnonymizedImage.name}
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                // Private View - Normal Edit Form
                <form onSubmit={handleSubmit} className="space-y-8">
                    {/* Basic Information Section */}
                    <div className="space-y-6">
                        <div className="flex items-center space-x-3 mb-6">
                            <div className="p-2 bg-green-50 rounded-lg">
                                <FileText className="w-5 h-5 text-green-600" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Basic Information</h2>
                                <p className="text-gray-600 text-sm mt-1">
                                    Update your listing&apos;s core details
                                </p>
                            </div>
                        </div>

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="lg:col-span-2">
                                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                                    Business Title *
                                </label>
                                <input
                                    type="text"
                                    id="title"
                                    value={listing.title}
                                    onChange={(e) => {
                                        setListing({ ...listing, title: e.target.value })
                                        // Clear validation error when user starts typing
                                        if (validationErrors.title) {
                                            setValidationErrors({ ...validationErrors, title: undefined })
                                        }
                                    }}
                                    className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:border-transparent transition-colors ${
                                        validationErrors.title 
                                            ? 'border-red-300 focus:ring-red-500' 
                                            : 'border-gray-300 focus:ring-blue-500'
                                    }`}
                                    placeholder="Enter your business title"
                                />
                                {validationErrors.title && (
                                    <p className="text-sm text-red-600 mt-1">{validationErrors.title}</p>
                                )}
                            </div>

                            <div className="lg:col-span-2">
                                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                                    Description *
                                </label>
                                <textarea
                                    id="description"
                                    value={listing.description}
                                    onChange={(e) => {
                                        setListing({ ...listing, description: e.target.value })
                                        // Clear validation error when user starts typing
                                        if (validationErrors.description) {
                                            setValidationErrors({ ...validationErrors, description: undefined })
                                        }
                                    }}
                                    rows={5}
                                    className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:border-transparent transition-colors resize-none ${
                                        validationErrors.description 
                                            ? 'border-red-300 focus:ring-red-500' 
                                            : 'border-gray-300 focus:ring-blue-500'
                                    }`}
                                    placeholder="Provide a detailed description of your business..."
                                />
                                {validationErrors.description && (
                                    <p className="text-sm text-red-600 mt-1">{validationErrors.description}</p>
                                )}
                            </div>

                            <div>
                                <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-2">
                                    Website URL
                                </label>
                                <div className="relative">
                                    <input
                                        type="url"
                                        id="website"
                                        value={listing.website || ''}
                                        onChange={(e) => setListing({ ...listing, website: e.target.value })}
                                        className="w-full px-4 py-3 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                                        placeholder="https://www.yourbusiness.com"
                                    />
                                    <Globe className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                </div>
                            </div>

                            <div>
                                <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">
                                    Asking Price *
                                </label>
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span className="text-gray-500 text-lg">$</span>
                                    </div>
                                    <input
                                        type="number"
                                        id="price"
                                        value={listing.price}
                                        onChange={(e) => {
                                            setListing({ ...listing, price: Number(e.target.value) })
                                            // Clear validation error when user starts typing
                                            if (validationErrors.price) {
                                                setValidationErrors({ ...validationErrors, price: undefined })
                                            }
                                        }}
                                        className={`w-full pl-8 pr-10 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:border-transparent transition-colors ${
                                            validationErrors.price 
                                                ? 'border-red-300 focus:ring-red-500' 
                                                : 'border-gray-300 focus:ring-blue-500'
                                        }`}
                                        placeholder="0"
                                        min="0"
                                    />
                                    <DollarSign className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                </div>
                                {validationErrors.price && (
                                    <p className="text-sm text-red-600 mt-1">{validationErrors.price}</p>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Industry Classification Section */}
                    <div className="border-t border-gray-200 pt-8">
                        <div className="flex items-center space-x-3 mb-6">
                            <div className="p-2 bg-orange-50 rounded-lg">
                                <Building2 className="w-5 h-5 text-orange-600" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Industry Classification</h2>
                                <p className="text-gray-600 text-sm mt-1">
                                    Categorize your business for better discoverability
                                </p>
                            </div>
                        </div>

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-2">
                                    Industry *
                                </label>
                                <div className="relative">
                                    <select
                                        id="industry"
                                        value={listing.industry_id}
                                        onChange={(e) => {
                                            setListing({
                                                ...listing,
                                                industry_id: e.target.value,
                                                sub_industry_id: null
                                            })
                                            // Clear validation error when user makes selection
                                            if (validationErrors.industry_id) {
                                                setValidationErrors({ ...validationErrors, industry_id: undefined })
                                            }
                                        }}
                                        className={`w-full px-4 py-3 pr-10 rounded-lg border focus:outline-none focus:ring-2 focus:border-transparent transition-colors appearance-none bg-white ${
                                            validationErrors.industry_id 
                                                ? 'border-red-300 focus:ring-red-500' 
                                                : 'border-gray-300 focus:ring-blue-500'
                                        }`}
                                    >
                                        <option value="">Select an industry</option>
                                        {industries.map((industry) => (
                                            <option key={industry.id} value={industry.id}>
                                                {industry.name}
                                            </option>
                                        ))}
                                    </select>
                                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                                </div>
                                {validationErrors.industry_id && (
                                    <p className="text-sm text-red-600 mt-1">{validationErrors.industry_id}</p>
                                )}
                            </div>

                            {listing.industry_id && (
                                <div>
                                    <label htmlFor="subIndustry" className="block text-sm font-medium text-gray-700 mb-2">
                                        Sub-Industry
                                    </label>
                                    <div className="relative">
                                        <select
                                            id="subIndustry"
                                            value={listing.sub_industry_id || ''}
                                            onChange={(e) => setListing({
                                                ...listing,
                                                sub_industry_id: e.target.value || null
                                            })}
                                            className="w-full px-4 py-3 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors appearance-none bg-white"
                                        >
                                            <option value="">Select a sub-industry (optional)</option>
                                            {subIndustries.map((subIndustry) => (
                                                <option key={subIndustry.id} value={subIndustry.id}>
                                                    {subIndustry.name}
                                                </option>
                                            ))}
                                        </select>
                                        <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Business Details Section */}
                    <div className="border-t border-gray-200 pt-8">
                        <div className="flex items-center justify-between mb-6 cursor-pointer" onClick={() => toggleSection('businessDetails')}>
                            <div className="flex items-center space-x-3">
                                <div className="p-2 bg-blue-50 rounded-lg">
                                    <Users className="w-5 h-5 text-blue-600" />
                                </div>
                                <div>
                                    <h2 className="text-xl font-semibold text-gray-900">Business Details</h2>
                                    <p className="text-gray-600 text-sm mt-1">
                                        Additional information about your business
                                    </p>
                                </div>
                            </div>
                            {expandedSections.businessDetails ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                        </div>

                        {expandedSections.businessDetails && (
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <label htmlFor="yearEstablished" className="block text-sm font-medium text-gray-700 mb-2">
                                        Year Established
                                    </label>
                                    <div className="relative">
                                        <input
                                            type="number"
                                            id="yearEstablished"
                                            value={listingDetails.year_established || ''}
                                            onChange={(e) => setListingDetails({
                                                ...listingDetails,
                                                year_established: e.target.value ? Number(e.target.value) : null
                                            })}
                                            className="w-full px-4 py-3 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                                            placeholder="e.g., 2020"
                                            min="1800"
                                            max={new Date().getFullYear()}
                                        />
                                        <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="legalStructure" className="block text-sm font-medium text-gray-700 mb-2">
                                        Legal Structure
                                    </label>
                                    <div className="relative">
                                        <select
                                            id="legalStructure"
                                            value={listingDetails.legal_structure || ''}
                                            onChange={(e) => setListingDetails({
                                                ...listingDetails,
                                                legal_structure: e.target.value || null
                                            })}
                                            className="w-full px-4 py-3 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors appearance-none bg-white"
                                        >
                                            <option value="">Select legal structure</option>
                                            <option value="LLC">LLC</option>
                                            <option value="Corporation">Corporation</option>
                                            <option value="Partnership">Partnership</option>
                                            <option value="Sole Proprietorship">Sole Proprietorship</option>
                                            <option value="Other">Other</option>
                                        </select>
                                        <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="teamSize" className="block text-sm font-medium text-gray-700 mb-2">
                                        Team Size
                                    </label>
                                    <div className="relative">
                                        <input
                                            type="number"
                                            id="teamSize"
                                            value={listingDetails.team_size || ''}
                                            onChange={(e) => setListingDetails({
                                                ...listingDetails,
                                                team_size: e.target.value ? Number(e.target.value) : null
                                            })}
                                            className="w-full px-4 py-3 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                                            placeholder="Number of employees"
                                            min="0"
                                        />
                                        <Users className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="activeCustomers" className="block text-sm font-medium text-gray-700 mb-2">
                                        Active Customers
                                    </label>
                                    <div className="relative">
                                        <input
                                            type="number"
                                            id="activeCustomers"
                                            value={listingDetails.active_customers || ''}
                                            onChange={(e) => setListingDetails({
                                                ...listingDetails,
                                                active_customers: e.target.value ? Number(e.target.value) : null
                                            })}
                                            className="w-full px-4 py-3 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                                            placeholder="Number of active customers"
                                            min="0"
                                        />
                                        <Hash className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="growthRate" className="block text-sm font-medium text-gray-700 mb-2">
                                        Growth Rate
                                    </label>
                                    <div className="relative">
                                        <input
                                            type="text"
                                            id="growthRate"
                                            value={listingDetails.growth_rate || ''}
                                            onChange={(e) => setListingDetails({
                                                ...listingDetails,
                                                growth_rate: e.target.value || null
                                            })}
                                            className="w-full px-4 py-3 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                                            placeholder="e.g., 25% YoY"
                                        />
                                        <Percent className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="reasonForSelling" className="block text-sm font-medium text-gray-700 mb-2">
                                        Reason for Selling
                                    </label>
                                    <div className="relative">
                                        <select
                                            id="reasonForSelling"
                                            value={listingDetails.reason_for_selling || ''}
                                            onChange={(e) => {
                                                const newReason = e.target.value || null
                                                setListingDetails({
                                                    ...listingDetails,
                                                    reason_for_selling: newReason
                                                })
                                                // Clear custom reason if not "Other"
                                                if (newReason !== 'Other') {
                                                    setCustomReason('')
                                                }
                                            }}
                                            className="w-full px-4 py-3 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors appearance-none bg-white"
                                        >
                                            <option value="">Select reason</option>
                                            <option value="Retirement">Retirement</option>
                                            <option value="New Opportunity">New Opportunity</option>
                                            <option value="Health Reasons">Health Reasons</option>
                                            <option value="Relocation">Relocation</option>
                                            <option value="Partnership Dissolution">Partnership Dissolution</option>
                                            <option value="Financial Reasons">Financial Reasons</option>
                                            <option value="Other">Other</option>
                                        </select>
                                        <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                                    </div>

                                    {/* Custom reason textarea - shown when "Other" is selected */}
                                    {listingDetails.reason_for_selling === 'Other' && (
                                        <div className="mt-4">
                                            <label htmlFor="customReason" className="block text-sm font-medium text-gray-700 mb-2">
                                                Please specify your reason
                                            </label>
                                            <div className="relative">
                                                <textarea
                                                    id="customReason"
                                                    value={customReason}
                                                    onChange={(e) => {
                                                        setCustomReason(e.target.value)
                                                        // Clear validation error when user starts typing
                                                        if (validationErrors.customReason) {
                                                            setValidationErrors({ ...validationErrors, customReason: undefined })
                                                        }
                                                    }}
                                                    rows={3}
                                                    className={`w-full px-4 py-3 pr-10 rounded-lg border focus:outline-none focus:ring-2 focus:border-transparent transition-colors resize-none ${
                                                        validationErrors.customReason 
                                                            ? 'border-red-300 focus:ring-red-500' 
                                                            : 'border-gray-300 focus:ring-blue-500'
                                                    }`}
                                                    placeholder="Please describe your reason for selling..."
                                                />
                                                <MessageSquare className="absolute right-3 top-3 w-5 h-5 text-gray-400" />
                                            </div>
                                            {validationErrors.customReason && (
                                                <p className="text-sm text-red-600 mt-1">{validationErrors.customReason}</p>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Location Details Section */}
                    <div className="border-t border-gray-200 pt-8">
                        <div className="flex items-center justify-between mb-6 cursor-pointer" onClick={() => toggleSection('locationDetails')}>
                            <div className="flex items-center space-x-3">
                                <div className="p-2 bg-purple-50 rounded-lg">
                                    <MapPin className="w-5 h-5 text-purple-600" />
                                </div>
                                <div>
                                    <h2 className="text-xl font-semibold text-gray-900">Location Details</h2>
                                    <p className="text-gray-600 text-sm mt-1">
                                        Business location and address information
                                    </p>
                                </div>
                            </div>
                            {expandedSections.locationDetails ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                        </div>

                        {expandedSections.locationDetails && (
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-2">
                                        State
                                    </label>
                                    <div className="relative">
                                        <select
                                            id="state"
                                            value={listingDetails.state_id || ''}
                                            onChange={(e) => setListingDetails({
                                                ...listingDetails,
                                                state_id: e.target.value || null
                                            })}
                                            className="w-full px-4 py-3 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors appearance-none bg-white"
                                        >
                                            <option value="">Select state</option>
                                            {states.map((state) => (
                                                <option key={state.id} value={state.id}>
                                                    {state.name}
                                                </option>
                                            ))}
                                        </select>
                                        <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-2">
                                        City
                                    </label>
                                    <div className="relative">
                                        <input
                                            type="text"
                                            id="city"
                                            value={listingDetails.city || ''}
                                            onChange={(e) => setListingDetails({
                                                ...listingDetails,
                                                city: e.target.value || null
                                            })}
                                            className="w-full px-4 py-3 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                                            placeholder="Enter city"
                                        />
                                        <MapPin className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="streetAddress" className="block text-sm font-medium text-gray-700 mb-2">
                                        Street Address
                                    </label>
                                    <div className="relative">
                                        <input
                                            type="text"
                                            id="streetAddress"
                                            value={listingDetails.street_address || ''}
                                            onChange={(e) => setListingDetails({
                                                ...listingDetails,
                                                street_address: e.target.value || null
                                            })}
                                            className="w-full px-4 py-3 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                                            placeholder="Enter street address"
                                        />
                                        <Building className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="postalCode" className="block text-sm font-medium text-gray-700 mb-2">
                                        ZIP/Postal Code
                                    </label>
                                    <div className="relative">
                                        <input
                                            type="text"
                                            id="postalCode"
                                            value={listingDetails.postal_code || ''}
                                            onChange={(e) => setListingDetails({
                                                ...listingDetails,
                                                postal_code: e.target.value || null
                                            })}
                                            className="w-full px-4 py-3 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                                            placeholder="Enter ZIP/postal code"
                                        />
                                        <Hash className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Financial Details Section */}
                    <div className="border-t border-gray-200 pt-8">
                        <div className="flex items-center justify-between mb-6 cursor-pointer" onClick={() => toggleSection('financialDetails')}>
                            <div className="flex items-center space-x-3">
                                <div className="p-2 bg-green-50 rounded-lg">
                                    <TrendingUp className="w-5 h-5 text-green-600" />
                                </div>
                                <div>
                                    <h2 className="text-xl font-semibold text-gray-900">Financial Information</h2>
                                    <p className="text-gray-600 text-sm mt-1">
                                        Revenue, profit, and financial metrics
                                    </p>
                                </div>
                            </div>
                            {expandedSections.financialDetails ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                        </div>

                        {expandedSections.financialDetails && (
                            <div className="space-y-6">
                                {/* Annual Revenue */}
                                <RevenueRangeSlider
                                    initialValue={{
                                        min: listingDetails.annual_revenue_ttm_min || 0,
                                        max: listingDetails.annual_revenue_ttm_max || 0
                                    }}
                                    onRevenueChange={handleRevenueChange}
                                />

                                {/* Annual Profit */}
                                <ProfitRangeSlider
                                    initialValue={{
                                        min: listingDetails.annual_net_profit_ttm_min || 0,
                                        max: listingDetails.annual_net_profit_ttm_max || 0
                                    }}
                                    onProfitChange={handleProfitChange}
                                />

                                {/* Last Month Revenue */}
                                <LastMonthRevenueSlider
                                    initialValue={{
                                        min: listingDetails.last_month_revenue_min || 0,
                                        max: listingDetails.last_month_revenue_max || 0
                                    }}
                                    onRevenueChange={handleLastMonthRevenueChange}
                                />

                                {/* Last Month Profit */}
                                <LastMonthProfitSlider
                                    initialValue={{
                                        min: listingDetails.last_month_profit_min || 0,
                                        max: listingDetails.last_month_profit_max || 0
                                    }}
                                    onProfitChange={handleLastMonthProfitChange}
                                />

                                {/* Recurring Revenue */}
                                <RecurringRevenueSlider
                                    initialValue={{
                                        min: listingDetails.recurring_revenue_min || 0,
                                        max: listingDetails.recurring_revenue_max || 0
                                    }}
                                    onRevenueChange={handleRecurringRevenueChange}
                                />
                            </div>
                        )}
                    </div>

                    {/* Image Management Section */}
                    <div className="border-t border-gray-200 pt-8">
                        <div className="flex items-center space-x-3 mb-6">
                            <div className="p-2 bg-red-50 rounded-lg">
                                <Camera className="w-5 h-5 text-red-600" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Business Image</h2>
                                <p className="text-gray-600 text-sm mt-1">
                                    Update your business listing image
                                </p>
                            </div>
                        </div>

                        <div className="space-y-6">
                            {/* Current Image Display */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-3">
                                    Current Image
                                </label>
                                <div className="relative w-full max-w-md h-48 rounded-lg overflow-hidden bg-gray-100">
                                    <Image
                                        src={imagePreview || listing.image_url || '/images/placeholder-listing-image.jpg'}
                                        alt={listing.title}
                                        fill
                                        className="object-cover"
                                    />
                                </div>
                            </div>

                            {/* Image Upload */}
                            <div>
                                <label htmlFor="image" className="block text-sm font-medium text-gray-700 mb-3">
                                    Upload New Image
                                </label>
                                <div className="flex items-center space-x-4">
                                    <input
                                        type="file"
                                        id="image"
                                        accept="image/*"
                                        onChange={handleImageChange}
                                        className="block w-full text-sm text-gray-500
                                            file:mr-4 file:py-2 file:px-4
                                            file:rounded-lg file:border-0
                                            file:text-sm file:font-medium
                                            file:bg-blue-50 file:text-blue-700
                                            hover:file:bg-blue-100 file:cursor-pointer
                                            cursor-pointer border border-gray-300 rounded-lg p-2"
                                    />
                                    {selectedImage && (
                                        <button
                                            type="button"
                                            onClick={clearImageSelection}
                                            className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                                            title="Clear selection"
                                        >
                                            <X className="w-5 h-5" />
                                        </button>
                                    )}
                                </div>
                                {selectedImage && (
                                    <p className="text-sm text-green-600 mt-2 flex items-center">
                                        <Info className="w-4 h-4 mr-1" />
                                        New image selected: {selectedImage.name}
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Listing Status Section */}
                    <div className="border-t border-gray-200 pt-8">
                        <div className="flex items-center space-x-3 mb-6">
                            <div className="p-2 bg-purple-50 rounded-lg">
                                <Store className="w-5 h-5 text-purple-600" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Listing Status</h2>
                                <p className="text-gray-600 text-sm mt-1">
                                    Control when your listing is visible to potential buyers
                                </p>
                            </div>
                        </div>

                        <div className="bg-gray-50 rounded-xl p-6 space-y-4">
                            <div className="flex items-center gap-4">
                                <button
                                    type="button"
                                    onClick={() => setListing({ ...listing, status: 'live' })}
                                    className={`flex-1 px-4 py-3 rounded-lg border transition-all duration-200 flex items-center justify-center gap-3 ${listing.status === 'live' || !listing.status
                                            ? 'bg-green-50 border-green-200 text-green-800'
                                            : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'
                                        }`}
                                >
                                    <div className={`w-3 h-3 rounded-full ${listing.status === 'live' || !listing.status ? 'bg-green-500' : 'bg-gray-300'
                                        }`} />
                                    <span className="font-medium">Live</span>
                                </button>
                                <button
                                    type="button"
                                    onClick={() => setListing({ ...listing, status: 'draft' })}
                                    className={`flex-1 px-4 py-3 rounded-lg border transition-all duration-200 flex items-center justify-center gap-3 ${listing.status === 'draft'
                                            ? 'bg-orange-50 border-orange-200 text-orange-800'
                                            : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'
                                        }`}
                                >
                                    <div className={`w-3 h-3 rounded-full ${listing.status === 'draft' ? 'bg-orange-500' : 'bg-gray-300'
                                        }`} />
                                    <span className="font-medium">Draft</span>
                                </button>
                            </div>

                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <p className="text-sm text-blue-800">
                                    {listing.status === 'draft'
                                        ? "📝 Your listing is in draft mode. Do you need more time to get ready and don't want your listing live on the marketplace? No worries, put it in draft and put it live when you want to. Potential buyers won't be able to see this listing."
                                        : "🌐 Your listing is live and visible to all potential buyers on the marketplace. It can receive inquiries and be found in search results."
                                    }
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="border-t border-gray-200 pt-8">
                        <div className="bg-gray-50 rounded-xl p-6 mb-6">
                            <div className="flex items-start space-x-4">
                                <div className="flex items-center h-5">
                                    <input
                                        type="checkbox"
                                        id="regenerateAnonymized"
                                        checked={regenerateAnonymized}
                                        onChange={(e) => setRegenerateAnonymized(e.target.checked)}
                                        className="w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 focus:ring-2 transition-colors"
                                    />
                                </div>
                                <div className="flex-1">
                                    <label htmlFor="regenerateAnonymized" className="text-sm font-medium text-gray-900 cursor-pointer block">
                                        Regenerate anonymized information
                                    </label>
                                    <p className="text-sm text-gray-600 mt-1 leading-relaxed">
                                        Automatically update the anonymized title and description based on your current listing changes to keep buyer view information in sync.
                                    </p>
                                </div>
                                <div className="flex-shrink-0">
                                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-colors ${regenerateAnonymized
                                        ? 'bg-blue-100 text-blue-800'
                                        : 'bg-gray-100 text-gray-600'
                                        }`}>
                                        {regenerateAnonymized ? 'Enabled' : 'Disabled'}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="flex flex-col sm:flex-row gap-3 justify-end">
                            <button
                                type="button"
                                onClick={() => router.push('/account/my-listings')}
                                className="px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                disabled={isLoading}
                                className="inline-flex items-center px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 font-medium disabled:bg-gray-400 disabled:cursor-not-allowed shadow-sm"
                            >
                                {isLoading ? (
                                    <>
                                        <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                                        Saving Changes...
                                    </>
                                ) : (
                                    <>
                                        <Save className="w-5 h-5 mr-2" />
                                        Save Changes
                                    </>
                                )}
                            </button>
                        </div>
                    </div>
                </form>
            )}

            {/* Action Buttons for Public View */}
            {isPublicView && (
                <div className="border-t border-gray-200 pt-8">
                    <div className="flex flex-col sm:flex-row gap-3 justify-end">
                        <button
                            type="button"
                            onClick={() => router.push('/account/my-listings')}
                            className="px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                        >
                            Cancel
                        </button>
                        <button
                            type="button"
                            onClick={(e) => {
                                e.preventDefault()
                                handleSubmit(e as React.FormEvent)
                            }}
                            disabled={isLoading}
                            className="inline-flex items-center px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 font-medium disabled:bg-gray-400 disabled:cursor-not-allowed shadow-sm"
                        >
                            {isLoading ? (
                                <>
                                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                                    Saving Changes...
                                </>
                            ) : (
                                <>
                                    <Save className="w-5 h-5 mr-2" />
                                    Save Changes
                                </>
                            )}
                        </button>
                    </div>
                </div>
            )}
        </div>
    )
}
