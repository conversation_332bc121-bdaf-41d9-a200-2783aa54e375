'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { PriceFormatter } from '@/components'
import { Store, FolderLock, Pencil, Trash2, Eye, Plus, Building } from 'lucide-react'
import Image from 'next/image'

interface Listing {
    id: string
    title: string
    description: string
    price: number
    image_url: string
    website?: string
    created_at: string
    status: 'live' | 'draft'
    industry_id: string
    industries?: {
        id: string
        name: string
    } | null
}

interface Props {
    initialListings: Listing[]
}

interface DeleteModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    isLoading: boolean;
}

function DeleteModal({ isOpen, onClose, onConfirm, isLoading }: DeleteModalProps) {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 max-w-sm mx-4 w-full shadow-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Delete Listing</h3>
                <p className="text-gray-600 mb-6">
                    Are you sure you want to delete this listing? This action cannot be undone.
                </p>
                <div className="flex gap-3">
                    <button
                        onClick={onClose}
                        className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={onConfirm}
                        disabled={isLoading}
                        className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 font-medium"
                    >
                        {isLoading ? 'Deleting...' : 'Delete'}
                    </button>
                </div>
            </div>
        </div>
    );
}

export default function MyListingsClient({ initialListings }: Props) {
    const [listings, setListings] = useState<Listing[]>(initialListings)
    const [isLoading, setIsLoading] = useState(false)
    const [showDeleteModal, setShowDeleteModal] = useState(false)
    const [listingToDelete, setListingToDelete] = useState<string | null>(null)
    const router = useRouter()

    const handleDeleteClick = (id: string) => {
        setListingToDelete(id);
        setShowDeleteModal(true);
    };

    const handleDelete = async () => {
        if (!listingToDelete) return;
        setIsLoading(true);

        try {
            // Call the server-side delete API that can handle foreign key constraints properly
            const response = await fetch('/api/listings/delete', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ listingId: listingToDelete }),
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Failed to delete listing');
            }

            // Update local state to remove the deleted listing
            setListings(listings.filter(listing => listing.id !== listingToDelete));
            router.refresh();
            console.log('✅ Listing deleted successfully');
        } catch (error) {
            console.error('❌ Failed to delete listing:', error);
            alert(`Failed to delete listing: ${error instanceof Error ? error.message : 'Unknown error'}`);
        } finally {
            setIsLoading(false);
            setShowDeleteModal(false);
            setListingToDelete(null);
        }
    };

    if (listings.length === 0) {
        return (
            <div className="space-y-8">
                {/* Header Card */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                    <div className="flex items-center space-x-3 mb-2">
                        <div className="p-2 bg-green-50 rounded-lg">
                            <Store className="w-5 h-5 text-green-600" />
                        </div>
                        <h1 className="text-2xl font-semibold text-gray-900">My Listings</h1>
                    </div>
                    <p className="text-gray-600 text-sm">Manage and track your business listings</p>
                </div>

                {/* Empty State Card */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-12">
                    <div className="flex flex-col items-center max-w-md mx-auto text-center">
                        <div className="p-4 bg-green-50 rounded-full mb-6">
                            <Store className="w-12 h-12 text-green-600" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-3">Create Your First Listing</h3>
                        <p className="text-gray-600 mb-8 leading-relaxed">
                            Start selling your business by creating your first listing.
                            It only takes a few minutes to reach thousands of potential buyers.
                        </p>
                        <Link
                            href="/listings/add-listing"
                            className="inline-flex items-center px-6 py-3 bg-neutral-800 text-white rounded-lg hover:bg-neutral-700 transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-500 font-medium group"
                        >
                            <Plus className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                            Create Listing
                        </Link>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="space-y-8">
            {/* Header Card */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-6 sm:p-8">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div className="flex items-center space-x-3">
                        <div className="p-2 bg-green-50 rounded-lg">
                            <Store className="w-5 h-5 text-green-600" />
                        </div>
                        <div>
                            <h1 className="text-2xl font-semibold text-gray-900">My Listings</h1>
                            <p className="text-gray-600 text-sm mt-1">
                                {listings.length} {listings.length === 1 ? 'listing' : 'listings'} active
                            </p>
                        </div>
                    </div>
                    <Link
                        href="/listings/add-listing"
                        className="inline-flex items-center justify-center w-full sm:w-auto px-4 py-2 bg-neutral-800 text-white rounded-lg hover:bg-neutral-700 transition-colors font-medium group"
                    >
                        <Plus className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                        New Listing
                    </Link>
                </div>
            </div>

            {/* Listings Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {listings.map((listing) => (
                    <div
                        key={listing.id}
                        className="bg-white rounded-xl shadow-sm border border-gray-200/60 overflow-hidden hover:shadow-md transition-shadow"
                    >
                        {/* Image Section */}
                        <div className="relative h-48">
                            <Image
                                src={listing.image_url || '/images/placeholder-listing-image.jpg'}
                                alt={listing.title}
                                fill
                                className="object-cover"
                            />
                            <div className="absolute top-3 left-3">
                                <div
                                    className={`inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium shadow-sm ${listing.status === 'live'
                                        ? 'bg-green-100 text-green-800 border border-green-200'
                                        : 'bg-orange-100 text-orange-800 border border-orange-200'
                                        }`}
                                    title={
                                        listing.status === 'live'
                                            ? 'This listing is live and visible to buyers on the marketplace'
                                            : 'This listing is in draft mode and not visible to buyers'
                                    }
                                >
                                    <div
                                        className={`w-2 h-2 rounded-full flex-shrink-0 ${listing.status === 'live' ? 'bg-green-500' : 'bg-orange-500'
                                            }`}
                                    />
                                    <span className="text-xs font-medium leading-none">
                                        {listing.status === 'live' ? 'Live' : 'Draft'}
                                    </span>
                                </div>
                            </div>
                            <div className="absolute top-3 right-3">
                                <div className="inline-flex items-center gap-1.5 bg-white/95 backdrop-blur-sm px-2.5 py-1.5 rounded-full border border-white/20 shadow-sm">
                                    <Building className="w-3 h-3 text-gray-600 flex-shrink-0" />
                                    <span className="text-xs font-medium text-gray-700 whitespace-nowrap">
                                        {listing.industries?.name || 'Business'}
                                    </span>
                                </div>
                            </div>
                        </div>

                        {/* Content Section */}
                        <div className="p-6">
                            <h2 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{listing.title}</h2>
                            <p className="text-gray-600 text-sm mb-4 line-clamp-3">{listing.description}</p>

                            <div className="mb-6">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-500">Asking Price</span>
                                    <PriceFormatter
                                        price={listing.price}
                                        className="text-lg font-semibold text-gray-900"
                                    />
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="space-y-3">
                                <div className="flex gap-2">
                                    <Link
                                        href={`/listings/${listing.id}`}
                                        className="flex items-center justify-center flex-1 px-3 py-2 bg-gray-50 text-gray-700 border border-gray-200 rounded-lg hover:bg-gray-100 hover:border-gray-300 transition-colors text-sm font-medium group"
                                    >
                                        <Eye className="w-4 h-4 mr-1.5 group-hover:scale-110 transition-transform" />
                                        View
                                    </Link>
                                    <Link
                                        href={`/account/my-listings/edit/${listing.id}`}
                                        className="flex items-center justify-center flex-1 px-3 py-2 bg-gray-50 text-gray-700 border border-gray-200 rounded-lg hover:bg-gray-100 hover:border-gray-300 transition-colors text-sm font-medium group"
                                    >
                                        <Pencil className="w-4 h-4 mr-1.5 group-hover:scale-110 transition-transform" />
                                        Edit
                                    </Link>
                                    <button
                                        onClick={() => handleDeleteClick(listing.id)}
                                        className="flex items-center justify-center px-3 py-2 bg-red-50 text-red-600 border border-red-200 rounded-lg hover:bg-red-100 hover:border-red-300 transition-colors text-sm font-medium group"
                                    >
                                        <Trash2 className="w-4 h-4 group-hover:scale-110 transition-transform" />
                                    </button>
                                </div>

                                <Link
                                    href={`/listings/${listing.id}/data-room`}
                                    className="flex items-center justify-center w-full px-3 py-2 bg-blue-50 text-blue-700 border border-blue-200 rounded-lg hover:bg-blue-100 hover:border-blue-300 transition-colors text-sm font-medium group"
                                >
                                    <FolderLock className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                                    Manage Data Room
                                </Link>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            <DeleteModal
                isOpen={showDeleteModal}
                onClose={() => setShowDeleteModal(false)}
                onConfirm={handleDelete}
                isLoading={isLoading}
            />
        </div>
    )
}

