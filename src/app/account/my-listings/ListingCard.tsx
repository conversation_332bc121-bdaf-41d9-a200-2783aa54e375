import { ReactNode } from 'react';

interface Listing {
  id: string;
  title: string;
  price: number;
  location: string;
  industry: string;
  description: string;
  createdAt: string;
}

interface ListingCardProps {
  listing: Listing;
  actions?: ReactNode;
}

export default function ListingCard({ listing, actions }: ListingCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold mb-2">{listing.title}</h3>
      <p className="text-green-600 font-medium mb-2">
        {listing.price === 0 ? 'Not Disclosed' : `$${listing.price}`}
      </p>
      <p className="text-gray-600 mb-2">{listing.location}</p>
      <p className="text-gray-500 text-sm mb-4">{listing.description}</p>
      {actions}
    </div>
  );
}
