'use client';

import Link from 'next/link';
import Image from 'next/image';
import { PriceFormatter } from '@/components';
import { ListingWithProfile } from '@/types/listing';
import { TrendingUp, ArrowRight, MapPin, Calendar } from 'lucide-react';
import { isListingNew, formatDateShort } from '@/utils/dateUtils';
import { FavoriteButton } from '../listings/components/FavoriteButton';
import { PerfectMatchButton } from '../listings/components/PerfectMatchButton';
import { ViewModeProvider } from '@/contexts/ViewModeContext';
import { AccessControlProvider } from '@/contexts/AccessControlContext';
import { useAccessControlledDisplay } from '@/hooks/useAccessControlledDisplay';
import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';

interface NewestListingsProps {
    listings: ListingWithProfile[];
}

export function NewestListings({ listings }: NewestListingsProps) {
    const supabase = createClient()
    const [userId, setUserId] = useState<string | undefined>(undefined)

    useEffect(() => {
        const fetchUserId = async () => {
            const { data: { session } } = await supabase.auth.getSession()
            setUserId(session?.user?.id)
        }
        fetchUserId()
    }, [supabase])

    return (
        <div className="bg-gray-50 py-16">
            <div className="container mx-auto px-6 sm:px-8">
                {/* Header Card */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                        <div className="flex flex-col gap-3">
                            <div className="flex items-center space-x-3">
                                <div className="p-2 bg-green-50 rounded-lg">
                                    <TrendingUp className="w-5 h-5 text-green-600" />
                                </div>
                                <h1 className="text-2xl font-semibold text-gray-900">Latest Businesses For Sale</h1>
                            </div>
                            <p className="text-gray-600 text-sm">
                                Discover the newest opportunities in our marketplace
                            </p>
                        </div>
                        <div className="flex justify-start md:justify-end">
                            <Link
                                href="/listings"
                                className="inline-flex items-center justify-center w-full md:w-auto px-4 py-3 bg-neutral-800 text-white rounded-lg hover:bg-neutral-700 transition-colors font-[550] tracking-wide group"
                            >
                                <span>View All Businesses</span>
                                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Listings Grid */}
                {listings.length === 0 ? (
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-12">
                        <div className="flex flex-col items-center max-w-md mx-auto text-center">
                            <div className="p-4 bg-gray-50 rounded-full mb-6">
                                <TrendingUp className="w-12 h-12 text-gray-400" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-3">No Listings Available</h3>
                            <p className="text-gray-600 mb-8 leading-relaxed">
                                New businesses are added regularly. Check back soon for the latest opportunities.
                            </p>
                            <Link
                                href="/listings"
                                className="inline-flex items-center px-6 py-3 bg-neutral-800 text-white rounded-lg hover:bg-neutral-700 transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-500 font-[550] tracking-wide group"
                            >
                                <TrendingUp className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                                Browse All Listings
                            </Link>
                        </div>
                    </div>
                ) : (
                    <ViewModeProvider>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {listings.map((listing) => {
                            return (
                                <AccessControlProvider key={listing.id} listingId={listing.id} userId={userId}>
                                <ListingCard key={listing.id} listing={listing} />
                                </AccessControlProvider>
                            );
                        })}
                    </div>
                    </ViewModeProvider>
                )}
            </div>
        </div>
    );
}

function ListingCard({ listing }: { listing: ListingWithProfile }) {
    const isNew = isListingNew(listing.created_at);

    // Use the new hook to get access-controlled display values
    const { displayTitle, displayDescription, displayImageUrl } = useAccessControlledDisplay({
        realTitle: listing.title,
        realDescription: listing.description,
        realImageUrl: listing.image_url,
        anonymizedDetails: listing.listing_anonymized_details
    });

    return (
        <div
            className="relative bg-white rounded-xl shadow-sm border border-gray-200/60 overflow-hidden hover:shadow-md transition-all duration-200 group h-full flex flex-col"
        >
            {/* Perfect Match Button - positioned left of favorite button */}
            <PerfectMatchButton
                listingId={listing.id}
                listingUserId={listing.user_id}
            />

            {/* Favorite Button */}
            <FavoriteButton
                listingId={listing.id}
                userId={listing.user_id}
            />

            <Link href={`/listings/${listing.id}`} className="flex flex-col h-full">
                {/* Image Section - Fixed height */}
                <div className="relative h-48 flex-shrink-0">
                    {displayImageUrl ? (
                        <Image
                            src={displayImageUrl}
                            alt={displayTitle}
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            className="object-cover group-hover:scale-105 transition-transform duration-200"
                        />
                    ) : (
                        <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                            <span className="text-gray-400 font-medium">No Image Available</span>
                        </div>
                    )}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>

                    {/* Industry Badge */}
                    <div className="absolute top-3 left-3">
                        <span className="px-2 py-1 bg-white/90 backdrop-blur-sm text-xs font-medium text-gray-700 rounded-full">
                            {listing.industries?.name || 'Uncategorized'}
                        </span>
                    </div>
                </div>

                {/* Content Section - Flexible height */}
                <div className="p-6 flex flex-col flex-grow">
                    {/* Title - Fixed height */}
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors min-h-[3.5rem]">
                        {displayTitle}
                    </h3>

                    {/* Description - Fixed height with line clamping */}
                        <p className="text-gray-600 text-sm mb-4 line-clamp-3 leading-relaxed min-h-[4.5rem]">
                            {displayDescription}
                        </p>

                    {/* Location - Fixed height area */}
                    <div className="mb-4 min-h-[1.5rem] flex items-start">
                        {listing.listing_details?.[0]?.city && (
                            <div className="flex items-center text-sm text-gray-500">
                                <MapPin className="w-4 h-4 mr-1 flex-shrink-0" />
                                <span>
                                    {listing.listing_details[0].city}
                                    {listing.listing_details[0].states && `, ${listing.listing_details[0].states.code}`}
                                </span>
                            </div>
                        )}
                    </div>

                    {/* Spacer to push footer to bottom */}
                    <div className="flex-grow"></div>

                    {/* Footer - Always at bottom */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-100 mt-auto">
                        {/* Date with optional "new" indicator */}
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                            <Calendar className="w-4 h-4 flex-shrink-0" />
                            <span className="whitespace-nowrap">{formatDateShort(listing.created_at)}</span>
                            {isNew && (
                                <span className="px-2 py-1 bg-gray-200 text-gray-600 text-xs font-medium rounded-full whitespace-nowrap">
                                    NEW
                                </span>
                            )}
                        </div>

                        {/* Price */}
                        <PriceFormatter
                            price={listing.price}
                            className="text-lg font-semibold text-gray-900"
                        />
                    </div>
                </div>
            </Link>
        </div>
    );
} 