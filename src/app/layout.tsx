import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import './globals.css';
import { ListingFormModalProvider } from '@/contexts/ListingFormModalContext';
import { UserProvider } from '@/contexts/UserContext';
import ListingFormModal from '@/components/modals/ListingFormModal';
import Script from 'next/script';
import { Toaster } from 'sonner';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: {
    default: 'Evermark - Your Trusted Marketplace',
    template: '%s | Evermark',
  },
  description:
    'Evermark is your go-to marketplace for buying and selling SMBs. Connect with trusted sellers and find profitable businesses in your area.',
  keywords: ['marketplace', 'buy', 'sell', 'SMBs', 'business marketplace'],
  authors: [
    {
      name: 'Evermark Team',
    },
  ],
  openGraph: {
    title: 'Evermark - Your Trusted Marketplace',
    description:
      'AI-powered marketplace for buying and selling SMBs. Connect with trusted sellers and find profitable businesses with Evermark.',
    url: 'https://evermark.ai',
    siteName: 'Evermark',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Evermark - AI-Powered SMB Marketplace',
    description:
      'AI-powered marketplace for buying and selling SMBs. Connect with trusted sellers and find profitable businesses with Evermark.',
  },
  robots: {
    index: true,
    follow: true,
  },
  metadataBase: new URL('https://evermark.ai'),
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Google Analytics */}
        <Script
          strategy="afterInteractive"
          src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
        />
        <Script
          id="google-analytics"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
            `,
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        <UserProvider>
          <ListingFormModalProvider>
            {children}
            <ListingFormModal />
            <Toaster richColors position="top-right" />
          </ListingFormModalProvider>
        </UserProvider>
      </body>
    </html>
  );
}
