import Hero from "@/components/Hero";
import { Header, Footer } from '@/components';
import { NewestListings } from './components/NewestListings';
import { createClient } from '@/utils/supabase/server';
import { CallToAction } from './components/CallToAction';
import { ListingWithProfile } from '@/types/listing';

async function getNewestListings(): Promise<ListingWithProfile[]> {
    const supabase = await createClient();

    try {
        const { data, error } = await supabase
            .from('listings')
            .select(`
        *,
        industries (
          id,
          name
        ),
        listing_anonymized_details!listing_id (
          anonymous_title,
          anonymous_description,
          anonymous_image_url
        )
      `)
            .eq('status', 'live')
            .order('created_at', { ascending: false })
            .limit(3);

        if (error) {
            console.error('Supabase error:', error.message);
            return [];
        }

        return data || [];
    } catch (error) {
        console.error('Error fetching newest listings:', error);
        return [];
    }
}

export default async function HomePageWithinGroup() { // Renamed function slightly for clarity
    const newestListings = await getNewestListings();

    return (
        <div className="flex flex-col min-h-screen">
            <Header />
            <main className="flex flex-col flex-1">
                <Hero />
                <NewestListings listings={newestListings} />
                <CallToAction />
            </main>
            <Footer />
        </div>
    );
} 