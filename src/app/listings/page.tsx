import { createClient } from '@/utils/supabase/server';
import { Listing } from '@/types/listing';
import { Industry, INDUSTRY_CONFIG } from '@/config/industries';
import { LayoutGrid, List, Store } from 'lucide-react';
import { Hero } from '@/components';
import { SortSelect } from './components/SortSelect';
import { FilterButton } from './components/FilterButton';
import { SavedListingProvider } from '@/contexts/SavedListingContext';
import { NoResults } from './components/NoResults';
import { ListingCard } from './components/ListingCard';
import { ViewModeProvider } from '@/contexts/ViewModeContext';
import { AccessControlProvider } from '@/contexts/AccessControlContext';

// Add Profile interface since it's not in the types file yet
interface Profile {
    user_id: string;
    first_name: string | null;
    last_name: string | null;
    profile_photo?: string;
}

// Add interface for listing anonymized details
interface ListingAnonymizedDetails {
    anonymous_title: string;
    anonymous_description: string | null;
    anonymous_image_url: string | null;
}

// Add interface for combined listing with profile
interface ListingWithProfile extends Listing {
    profiles?: Profile;
    industries?: {
        id: string;
        name: string;
    };
    listing_details?: {
        street_address: string | null;
        city: string | null;
        postal_code: string | null;
        states: {
            id: string;
            name: string;
            code: string;
        } | null;
    }[];
    listing_anonymized_details?: ListingAnonymizedDetails;
}

// Add this type for sort options
type SortOption = {
    label: string;
    value: string;
    orderBy: string;
    ascending: boolean;
};

const sortOptions: SortOption[] = [
    { label: 'Newest First', value: 'newest', orderBy: 'created_at', ascending: false },
    { label: 'Oldest First', value: 'oldest', orderBy: 'created_at', ascending: true },
    { label: 'Price: Low to High', value: 'price_asc', orderBy: 'price', ascending: true },
    { label: 'Price: High to Low', value: 'price_desc', orderBy: 'price', ascending: false },
    { label: 'Title: A to Z', value: 'title_asc', orderBy: 'title', ascending: true },
    { label: 'Title: Z to A', value: 'title_desc', orderBy: 'title', ascending: false },
];

type PageProps = {
    params: Promise<Record<string, never>>;
    searchParams: Promise<{ [key: string]: string | undefined }>;
}

// Add this to ensure the page revalidates on each request
export const revalidate = 0;

export default async function ListingsPage({
    searchParams
}: PageProps) {
    const resolvedSearchParams = await searchParams;
    const currentIndustries = resolvedSearchParams.industries?.split(',') as Industry[] || [];
    const currentSort = typeof resolvedSearchParams.sort === 'string' ? resolvedSearchParams.sort : 'newest';
    const searchQuery = typeof resolvedSearchParams.search === 'string' ? resolvedSearchParams.search : undefined;
    const currentState = typeof resolvedSearchParams.state === 'string' ? resolvedSearchParams.state : undefined;

    const supabase = await createClient();
    
    // Get the current user's session
    const { data: { session } } = await supabase.auth.getSession();
    const userId = session?.user?.id;
    
    // If user is not authenticated, we'll show public view
    const isPublicView = !userId;

    try {
        let query = supabase
            .from('listings')
            .select(`
                *,
                industries (
                    id,
                    name
                ),
                listing_details!left (
                    state_id,
                    street_address,
                    city,
                    states (
                        id,
                        name,
                        code
                    )
                ),
                listing_anonymized_details!left (*)
            `)
            .eq('status', 'live');

        if (currentIndustries.length > 0) {
            const industryIds = await Promise.all(
                currentIndustries.map(async (industry) => {
                    const industryConfig = INDUSTRY_CONFIG.find(i => i.value === industry);
                    if (industryConfig) {
                        const { data } = await supabase
                            .from('industries')
                            .select('id')
                            .ilike('name', industryConfig.label)
                            .single();
                        return data?.id;
                    }
                    return null;
                })
            );

            const validIds = industryIds.filter(id => id !== null);
            if (validIds.length > 0) {
                query = query.in('industry_id', validIds);
            }
        }

        if (currentState) {
            query = query.not('listing_details', 'is', null)
                .eq('listing_details.state_id', currentState);
        }

        if (searchQuery) {
            query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
        }

        const { data: listings, error: listingsError } = await query.order(
            sortOptions.find(opt => opt.value === currentSort)?.orderBy || 'created_at',
            { ascending: sortOptions.find(opt => opt.value === currentSort)?.ascending ?? false }
        );

        if (listingsError) throw listingsError;

        const userIds = [...new Set(listings?.map(listing => listing.user_id) || [])];

        const { data: profiles, error: profilesError } = await supabase
            .from('profiles')
            .select('*')
            .in('user_id', userIds);

        if (profilesError) throw profilesError;

        const listingsWithProfiles = listings?.map(listing => ({
            ...listing,
            profiles: profiles?.find(profile => profile.user_id === listing.user_id) || null
        })) || [];

        return (
            <SavedListingProvider>
                <ViewModeProvider initialIsPublic={isPublicView}>
                    <main className="min-h-screen bg-gray-50">
                        <Hero />

                        <div className="max-w-7xl mx-auto p-6">
                            {/* Header Card */}
                            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 lg:gap-0">
                                    <div className="flex flex-col gap-3">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-blue-50 rounded-lg">
                                                <Store className="w-5 h-5 text-blue-600" />
                                            </div>
                                            <h1 className="text-2xl font-semibold text-gray-900">
                                                <span className="text-gray-500 mr-2">{listingsWithProfiles.length}</span>
                                                {listingsWithProfiles.length === 1 ? 'Listing' : 'Listings'}
                                            </h1>
                                        </div>
                                        <p className="text-gray-600 text-sm">
                                            Discover businesses for sale across all industries
                                        </p>
                                    </div>

                                    {/* Desktop Controls */}
                                    <div className="hidden lg:flex items-center gap-4">
                                        <SortSelect currentSort={currentSort} />
                                        <div className="h-8 w-px bg-gray-200"></div>
                                        <FilterButton currentIndustries={currentIndustries} />
                                        <div className="h-8 w-px bg-gray-200"></div>
                                        <div className="flex items-center gap-1 bg-gray-50 border rounded-lg p-1">
                                            <button
                                                className="p-2 rounded-md bg-white text-gray-900 shadow-sm"
                                                aria-label="Grid view"
                                            >
                                                <LayoutGrid size={20} />
                                            </button>
                                            <button
                                                className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-50"
                                                aria-label="List view"
                                            >
                                                <List size={20} />
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                {/* Mobile Controls */}
                                <div className="lg:hidden mt-4 flex flex-col gap-3">
                                    {/* Row 1: SortSelect */}
                                    <div className="w-full">
                                        <SortSelect currentSort={currentSort} />
                                    </div>

                                    {/* Row 2: FilterButton + Layout buttons */}
                                    <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0">
                                            <FilterButton currentIndustries={currentIndustries} />
                                        </div>
                                        <div className="flex items-center gap-0.5 bg-gray-50 border rounded-lg p-0.5 flex-shrink-0">
                                            <button
                                                className="p-1.5 rounded-md bg-white text-gray-900 shadow-sm"
                                                aria-label="Grid view"
                                            >
                                                <LayoutGrid size={16} />
                                            </button>
                                            <button
                                                className="p-1.5 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-50"
                                                aria-label="List view"
                                            >
                                                <List size={16} />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Listings Content */}
                            {listingsWithProfiles.length === 0 ? (
                                <NoResults />
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {listingsWithProfiles.map((listing: ListingWithProfile) => (
                                        <AccessControlProvider key={listing.id} listingId={listing.id} userId={userId}>
                                            <ListingCard listing={listing} />
                                        </AccessControlProvider>
                                    ))}
                                </div>
                            )}
                        </div>
                    </main>
                </ViewModeProvider>
            </SavedListingProvider>
        );
    } catch (error) {
        console.error('Error in ListingsPage:', error);
        return <div>Error loading listings</div>;
    }
}