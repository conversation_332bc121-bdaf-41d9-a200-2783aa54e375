'use client';

import Link from 'next/link';
import Image from 'next/image';
import { User2, Calendar, MapPin } from 'lucide-react';
import { FavoriteButton } from './FavoriteButton';
import { PerfectMatchButton } from './PerfectMatchButton';
import { ListingWithProfile } from '@/types/listing';
import { isListingNew, formatDateShort } from '@/utils/dateUtils';
import { useViewMode } from '@/contexts/ViewModeContext';
import { useAccessControlledDisplay } from '@/hooks/useAccessControlledDisplay';

interface ListingCardProps {
  listing: ListingWithProfile;
}

export function ListingCard({ listing }: ListingCardProps) {
  const isNew = isListingNew(listing.created_at);
  const { isPublicView } = useViewMode();

  // Use the new hook to get access-controlled display values
  const { displayTitle, displayDescription, displayImageUrl } =
    useAccessControlledDisplay({
      realTitle: listing.title,
      realDescription: listing.description,
      realImageUrl: listing.image_url,
      anonymizedDetails: listing.listing_anonymized_details,
    });

  return (
    <div className="relative bg-white rounded-xl shadow-sm border border-gray-200/60 overflow-hidden hover:shadow-md transition-all duration-200 group h-full flex flex-col">
      {/* Perfect Match Button - positioned left of favorite button */}
      <PerfectMatchButton
        listingId={listing.id}
        listingUserId={listing.user_id}
      />

      {/* Favorite Button */}
      <FavoriteButton listingId={listing.id} userId={listing.user_id} />

      <Link href={`/listings/${listing.id}`} className="flex flex-col h-full">
        {/* Image Section - Fixed height */}
        <div className="relative h-48 flex-shrink-0">
          {displayImageUrl ? (
            <Image
              src={displayImageUrl}
              alt={displayTitle}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              className="object-cover group-hover:scale-105 transition-transform duration-200"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
              <span className="text-gray-400 font-medium">
                No Image Available
              </span>
            </div>
          )}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>

          {/* Industry Badge */}
          {listing.industries && (
            <div className="absolute top-3 left-3">
              <span className="px-2 py-1 bg-white/90 backdrop-blur-sm text-xs font-medium text-gray-700 rounded-full">
                {listing.industries.name}
              </span>
            </div>
          )}
        </div>

        {/* Content Section - Flexible height */}
        <div className="p-6 flex flex-col flex-grow">
          {/* Title and Price - Fixed height */}
          <div className="flex items-start justify-between mb-3 min-h-[3.5rem]">
            <h2 className="text-lg font-semibold text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors flex-1 mr-3">
              {displayTitle}
            </h2>
            <span className="text-lg font-semibold text-gray-900 whitespace-nowrap">
              {listing.price === 0
                ? 'Not Disclosed'
                : `$${listing.price.toLocaleString()}`}
            </span>
          </div>

          {/* Description - Fixed height with line clamping */}
          <p className="text-gray-600 text-sm mb-4 line-clamp-3 leading-relaxed min-h-[4.5rem]">
            {displayDescription}
          </p>

          {/* Location - Fixed height area */}
          <div className="mb-4 min-h-[1.5rem] flex items-start">
            {listing.listing_details?.[0]?.city && (
              <div className="flex items-center text-sm text-gray-500">
                <MapPin className="w-4 h-4 mr-1 flex-shrink-0" />
                <span>
                  {listing.listing_details[0].city}
                  {listing.listing_details[0].states &&
                    `, ${listing.listing_details[0].states.code}`}
                </span>
              </div>
            )}
          </div>

          {/* Spacer to push footer to bottom */}
          <div className="flex-grow"></div>

          {/* Footer - Always at bottom */}
          <div className="flex items-center justify-between text-sm text-gray-500 pt-4 border-t border-gray-100 mt-auto">
            {/* Date with optional "new" indicator */}
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 flex-shrink-0" />
              <span className="whitespace-nowrap">
                {formatDateShort(listing.created_at)}
              </span>
              {isNew && (
                <span className="px-2 py-1 bg-gray-200 text-gray-600 text-xs font-medium rounded-full whitespace-nowrap">
                  NEW
                </span>
              )}
            </div>

            {/* Profile info - Only show in private view */}
            <div className="flex items-center gap-2 min-w-0">
              {listing.profiles && !isPublicView && (
                <>
                  <span className="flex-shrink-0">
                    {listing.profiles.profile_photo ? (
                      <Image
                        src={listing.profiles.profile_photo}
                        alt={listing.profiles.first_name || 'User'}
                        width={20}
                        height={20}
                        className="rounded-full"
                      />
                    ) : (
                      <User2 className="w-4 h-4" />
                    )}
                  </span>
                  <span className="font-medium truncate">
                    {listing.profiles.first_name || listing.profiles.last_name
                      ? `${listing.profiles.first_name || ''} ${
                          listing.profiles.last_name || ''
                        }`.trim()
                      : 'Unknown User'}
                  </span>
                </>
              )}
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
}
