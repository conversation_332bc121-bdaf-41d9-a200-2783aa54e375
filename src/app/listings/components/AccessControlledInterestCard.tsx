'use client';

import { useAccessControlledDisplay } from '@/hooks/useAccessControlledDisplay';
import { Interest } from '@/types/listing';
import InterestCard from './InterestCard';

interface AccessControlledInterestCardProps {
    interest: Interest;
    realTitle: string;
    anonymizedTitle?: string | null;
}

export default function AccessControlledInterestCard({ 
    interest, 
    realTitle, 
    anonymizedTitle 
}: AccessControlledInterestCardProps) {
    const { displayTitle } = useAccessControlledDisplay({
        realTitle,
        anonymizedDetails: { anonymous_title: anonymizedTitle }
    });

    return (
        <InterestCard 
            interest={interest} 
            listingTitle={displayTitle}
        />
    );
} 