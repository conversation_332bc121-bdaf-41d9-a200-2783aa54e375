'use client';

import { useAccessControlledDisplay } from '@/hooks/useAccessControlledDisplay';

interface ListingDescriptionProps {
    realDescription: string | null;
    anonymousDescription: string | null;
}

export default function ListingDescription({ realDescription, anonymousDescription }: ListingDescriptionProps) {
    const { displayDescription } = useAccessControlledDisplay({
        realDescription,
        anonymizedDetails: { anonymous_description: anonymousDescription }
    });

    return (
        <p className="text-gray-600 whitespace-pre-wrap">
            {displayDescription}
        </p>
    );
} 