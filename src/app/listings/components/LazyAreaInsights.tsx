'use client';

import { useEffect, useState } from 'react';
import AreaInsights from './AreaInsights';
import { Loader2 } from 'lucide-react';

interface LazyAreaInsightsProps {
    postalCode?: string | null;
    fipsCode?: string | null;
    naicsCode?: string | null;
    address?: string | null;
    city?: string | null;
    state?: string | null;
    latitude?: number | null;
    longitude?: number | null;
}

interface WalkabilityData {
    walkScore?: number | null;
    isWalkScoreMock?: boolean;
    walkDescription?: string | null;
    transitScore?: number | null;
    isTransitScoreMock?: boolean;
    transitDescription?: string | null;
    bikeScore?: number | null;
    isBikeScoreMock?: boolean;
    bikeDescription?: string | null;
    wsLink?: string | null;
}

interface SimilarCompaniesData {
    numEstablishments?: number | null;
    totalAnnualPayroll?: number | null;
    totalEmployment?: number | null;
    averageAnnualWage?: number | null;
    countyName?: string | null;
    naicsDescription?: string | null;
    numEstablishmentsPreviousYear?: number | null;
    establishmentGrowthRate?: number | null;
    totalSalesReceipts?: number | null;
    avgSalesPerEstablishment?: number | null;
    isSalesDataMock?: boolean;
}

interface AreaInsightsData {
    city: string;
    state: string;
    population: number;
    isPopulationMock: boolean;
    medianIncome: number;
    isMedianIncomeMock: boolean;
    unemploymentRate: number;
    isUnemploymentRateMock: boolean;
    numBusinesses: number;
    isNumBusinessesMock: boolean;
    crimeRate: string;
    isCrimeStatsMock: boolean;
    schools: number;
    isSchoolCountMock: boolean;
    populationCAGR: number;
    isPopulationCAGRMock: boolean;
    medianAge: number;
    isMedianAgeMock: boolean;
    educationBachelorPlusPercent: number;
    isEducationBachelorPlusPercentMock: boolean;
}

interface AreaInsightsState {
    areaInsights: AreaInsightsData | null;
    similarCompaniesData: SimilarCompaniesData | null;
    walkScoreData: WalkabilityData | null;
    isLoading: boolean;
    error: string | null;
}

interface AreaInsightsRequestData {
    postalCode?: string;
    fipsCode?: string;
    naicsCode?: string;
    address?: string;
    city?: string;
    state?: string;
    latitude?: number;
    longitude?: number;
}

export default function LazyAreaInsights({
    postalCode,
    fipsCode,
    naicsCode,
    address,
    city,
    state,
    latitude,
    longitude
}: LazyAreaInsightsProps) {
    const [state_data, setState] = useState<AreaInsightsState>({
        areaInsights: null,
        similarCompaniesData: null,
        walkScoreData: null,
        isLoading: true,
        error: null
    });

    useEffect(() => {
        let isMounted = true;

        async function fetchAreaData() {
            try {
                setState(prev => ({ ...prev, isLoading: true, error: null }));

                // Validate and prepare request data according to API schema requirements
                const requestData: AreaInsightsRequestData = {};

                // Validate postalCode (must be 5 digits or 5+4 format)
                if (postalCode && typeof postalCode === 'string' && postalCode.trim()) {
                    const cleanPostalCode = postalCode.trim();
                    if (/^\d{5}(-\d{4})?$/.test(cleanPostalCode)) {
                        requestData.postalCode = cleanPostalCode;
                    }
                }

                // Validate fipsCode (must be exactly 5 digits)
                if (fipsCode && typeof fipsCode === 'string' && fipsCode.trim()) {
                    const cleanFipsCode = fipsCode.trim();
                    if (/^\d{5}$/.test(cleanFipsCode)) {
                        requestData.fipsCode = cleanFipsCode;
                    }
                }

                // Validate naicsCode (must be 2-6 digits)
                if (naicsCode && typeof naicsCode === 'string' && naicsCode.trim()) {
                    const cleanNaicsCode = naicsCode.trim();
                    if (/^\d{2,6}$/.test(cleanNaicsCode)) {
                        requestData.naicsCode = cleanNaicsCode;
                    }
                }

                // API requires at least postalCode or fipsCode
                if (!requestData.postalCode && !requestData.fipsCode) {
                    throw new Error('No valid postal code or FIPS code available for area insights');
                }

                // Add other optional parameters only if they have valid values
                if (address && typeof address === 'string' && address.trim()) {
                    requestData.address = address.trim();
                }
                if (city && typeof city === 'string' && city.trim()) {
                    requestData.city = city.trim();
                }
                if (state && typeof state === 'string' && state.trim()) {
                    requestData.state = state.trim();
                }
                if (typeof latitude === 'number' && !isNaN(latitude)) {
                    requestData.latitude = latitude;
                }
                if (typeof longitude === 'number' && !isNaN(longitude)) {
                    requestData.longitude = longitude;
                }

                // **OPTIMIZATION 11: Client-side parallel API calls with better UX**
                const response = await fetch('/api/area-insights', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`API responded with status: ${response.status}. ${errorText}`);
                }

                const data = await response.json();

                if (isMounted) {
                    setState({
                        areaInsights: data.areaInsights,
                        similarCompaniesData: data.similarCompanies,
                        walkScoreData: data.walkability,
                        isLoading: false,
                        error: null
                    });
                }
            } catch (error) {
                console.error('Error fetching area insights:', error);
                if (isMounted) {
                    setState(prev => ({
                        ...prev,
                        isLoading: false,
                        error: 'Failed to load area insights. Please try refreshing the page.'
                    }));
                }
            }
        }

        // Start fetching after a small delay to allow essential content to render first
        const timeoutId = setTimeout(fetchAreaData, 100);

        return () => {
            isMounted = false;
            clearTimeout(timeoutId);
        };
    }, [postalCode, fipsCode, naicsCode, address, city, state, latitude, longitude]);

    // **Loading State with Skeleton**
    if (state_data.isLoading) {
        return (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                <div className="flex items-center justify-center space-x-2 text-gray-500">
                    <Loader2 className="w-5 h-5 animate-spin" />
                    <span>Loading area insights...</span>
                </div>
                <div className="mt-4 space-y-3">
                    {/* Skeleton loading bars */}
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
                </div>
            </div>
        );
    }

    // **Error State**
    if (state_data.error) {
        return (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                <div className="text-center">
                    <div className="text-red-600 mb-2">⚠️ Unable to load area insights</div>
                    <p className="text-sm text-gray-600">{state_data.error}</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors"
                    >
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    // **Render with loaded data**
    if (state_data.areaInsights) {
        return (
            <AreaInsights
                areaInsights={state_data.areaInsights}
                similarCompaniesData={state_data.similarCompaniesData || {
                    numEstablishments: null,
                    totalAnnualPayroll: null,
                    totalEmployment: null,
                    averageAnnualWage: null,
                    countyName: null,
                    naicsDescription: null,
                    numEstablishmentsPreviousYear: null,
                    establishmentGrowthRate: null,
                    totalSalesReceipts: null,
                    avgSalesPerEstablishment: null,
                    isSalesDataMock: true,
                }}
                walkScoreData={state_data.walkScoreData || {
                    walkScore: null,
                    isWalkScoreMock: true,
                    walkDescription: null,
                    transitScore: null,
                    isTransitScoreMock: true,
                    transitDescription: null,
                    bikeScore: null,
                    isBikeScoreMock: true,
                    bikeDescription: null,
                    wsLink: null,
                }}
            />
        );
    }

    // **Fallback if no data loaded**
    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
            <div className="text-center text-gray-500">
                <div className="mb-2">📊 No area insights available</div>
                <p className="text-sm">Unable to load demographic data for this location.</p>
            </div>
        </div>
    );
} 