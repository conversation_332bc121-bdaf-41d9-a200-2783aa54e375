'use client';

import { Heart } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useSupabase } from '@/hooks/useSupabase';

interface FavoriteButtonProps {
    listingId: string;
    userId: string;
}

export function FavoriteButton({ listingId, userId }: FavoriteButtonProps) {
    const [isSaved, setIsSaved] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [isOwnListing, setIsOwnListing] = useState(false);
    const supabase = useSupabase();

    useEffect(() => {
        const checkOwnershipAndFavorited = async () => {
            try {
                const { data: { session } } = await supabase.auth.getSession();

                if (!session?.user) {
                    return;
                }

                setIsOwnListing(userId === session.user.id);

                if (userId !== session.user.id) {
                    const { data } = await supabase
                        .from('saved_listings')
                        .select('*')
                        .eq('listing_id', listingId)
                        .eq('user_id', session.user.id)
                        .maybeSingle();

                    setIsSaved(!!data);
                }
            } catch (error) {
                console.error('Error checking favorite status:', error);
            }
        };

        checkOwnershipAndFavorited();
    }, [listingId, userId, supabase]);

    if (isOwnListing) {
        return null;
    }

    const handleToggle = async () => {
        if (!listingId) return;

        if (isSaved) {
            setShowConfirmModal(true);
            return;
        }

        setIsLoading(true);

        try {
            const { data: { session } } = await supabase.auth.getSession();

            if (!session?.user) {
                window.location.href = '/login';
                return;
            }

            const { error } = await supabase
                .from('saved_listings')
                .insert({
                    listing_id: listingId,
                    user_id: session.user.id
                });

            if (error) throw error;
            setIsSaved(true);
        } catch (error) {
            console.error('Error saving listing:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleRemove = async () => {
        setIsLoading(true);
        try {
            const { data: { session } } = await supabase.auth.getSession();

            const { error } = await supabase
                .from('saved_listings')
                .delete()
                .match({
                    listing_id: listingId,
                    user_id: session!.user.id
                });

            if (error) throw error;
            setIsSaved(false);
        } catch (error) {
            console.error('Error removing listing:', error);
        } finally {
            setIsLoading(false);
            setShowConfirmModal(false);
        }
    };

    return (
        <>
            <button
                onClick={handleToggle}
                disabled={isLoading}
                className="absolute top-4 right-4 p-2 rounded-full bg-white/80 backdrop-blur-sm hover:bg-white transition-colors shadow-sm z-10"
            >
                <Heart
                    className={`w-5 h-5 ${isSaved ? 'fill-red-500 stroke-red-500' : 'stroke-gray-600'}`}
                />
            </button>

            {showConfirmModal && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-sm mx-4 w-full">
                        <h3 className="text-lg font-semibold mb-4">Remove from Saved</h3>
                        <p className="text-gray-600 mb-6">
                            Are you sure you want to remove this listing from your saved items?
                        </p>
                        <div className="flex gap-3">
                            <button
                                onClick={() => setShowConfirmModal(false)}
                                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleRemove}
                                disabled={isLoading}
                                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                            >
                                Remove
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
} 