'use client';

import { useAccessControlContext } from '@/contexts/AccessControlContext';

interface AccessControlDisplayProps {
  showMessageWhenAccessGranted?: boolean;
}

export default function AccessControlDisplay({ showMessageWhenAccessGranted = true }: AccessControlDisplayProps) {
  const { isOwner, hasAccess, isLoading } = useAccessControlContext();
  
  if (isLoading) return null;
  
  // Only show message when user has access but is not the owner
  if (showMessageWhenAccessGranted && hasAccess && !isOwner) {
    return (
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-sm text-blue-800 flex items-center">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          You have been granted access to this listing&apos;s private information. You&apos;re currently viewing the private data.
        </p>
      </div>
    );
  }
  
  return null;
} 