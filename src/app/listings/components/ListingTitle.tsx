'use client';

import { useAccessControlledDisplay } from '@/hooks/useAccessControlledDisplay';

interface ListingTitleProps {
    realTitle: string;
    anonymousTitle: string | null;
}

export default function ListingTitle({ realTitle, anonymousTitle }: ListingTitleProps) {
    const { displayTitle } = useAccessControlledDisplay({
        realTitle,
        anonymizedDetails: { anonymous_title: anonymousTitle }
    });

    return (
        <h1 className="text-3xl font-bold mb-2 text-white">
            {displayTitle}
        </h1>
    );
} 