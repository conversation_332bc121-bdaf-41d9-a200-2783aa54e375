'use client';

import { useAccessControlContext } from '@/contexts/AccessControlContext';
import ViewModeToggle from './ViewModeToggle';

export default function AccessAwareViewModeToggle() {
  const { isOwner, isLoading } = useAccessControlContext();

  // Don't render anything while loading to avoid flashing
  if (isLoading) return null;
  
  // Only show the toggle if the user is the owner
  if (isOwner) {
    return <ViewModeToggle />;
  }
  
  // Don't render the toggle for non-owners
  return null;
} 