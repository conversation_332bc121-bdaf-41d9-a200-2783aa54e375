import { createClient } from '@/utils/supabase/server'
import { MatchClient } from './MatchClient'
import { Metadata } from 'next'
import { ViewModeProvider } from '@/contexts/ViewModeContext'

export const metadata: Metadata = {
    title: 'Find Matching Listings - Business Marketplace',
    description: 'Set your preferences and find businesses that match your criteria.',
}

export default async function MatchPage() {
    const supabase = await createClient()

    // Fetch all industries for the form
    const { data: industries } = await supabase
        .from('industries')
        .select('id, name')
        .order('name')

    // For initial load, we don't need to fetch listings yet
    // They will be fetched based on user preferences through the client component

    return (
        <div className="container mx-auto px-4">
            <ViewModeProvider >
            <MatchClient industries={industries || []} />
            </ViewModeProvider>
        </div>
    )
} 