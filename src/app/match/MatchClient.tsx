'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/utils/supabase/client'
import { Loader2, ChevronDown, ChevronUp, Plus, Edit, Trash, Save, X, Target, Search, Settings, TrendingUp } from 'lucide-react'
import ListingCard from './components/ListingCard'
import { LocationAutocomplete } from '@/components'
import { AccessControlProvider } from '@/contexts/AccessControlContext'

// Define the preference types
interface MatchPreferences {
    priceMin: number
    priceMax: number
    revenueMin: number
    revenueMax: number
    industryId: string | null
    subIndustryId: string | null
    location: string | null
    // New structured location fields
    locationCity: string | null
    locationState: string | null
    locationStateId: string | null
    locationCounty: string | null
    yearEstablishedMin: number | null
}

// Define the listing type
interface Listing {
    id: string
    title: string
    description: string | null
    price: number
    image_url: string | null
    slug: string | null
    user_id: string
    industries: { id: string; name: string } | null
    sub_industries: { id: string; name: string; industry_id: string } | null
    listing_details: {
        year_established: number | null
        location: string | null
        city: string | null
        state_id: string | null
        postal_code: string | null
        annual_revenue_ttm_min: number | null
        annual_revenue_ttm_max: number | null
    } | null
    listing_anonymized_details: {
        anonymous_title: string | null
        anonymous_description: string | null
        anonymous_image_url: string | null
    } | null
}

// Define the saved match type
interface SavedMatch {
    id: string
    name: string
    preferences: MatchPreferences
    created_at: string
    updated_at: string
    isExpanded?: boolean
    matches?: Listing[]
    isLoading?: boolean
}

interface MatchClientProps {
    industries: Array<{ id: string; name: string }>
}

// Custom Select Component
function CustomSelect({
    value,
    onChange,
    options,
    placeholder,
    disabled = false,
    className = ""
}: {
    value: string
    onChange: (value: string) => void
    options: Array<{ value: string; label: string }>
    placeholder: string
    disabled?: boolean
    className?: string
}) {
    return (
        <div className={`relative ${className}`}>
            <select
                value={value}
                onChange={(e) => onChange(e.target.value)}
                disabled={disabled}
                className={`
                    w-full border border-gray-300 rounded-lg p-3 pr-10 
                    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                    disabled:bg-gray-100 disabled:cursor-not-allowed
                    appearance-none bg-white cursor-pointer
                    text-gray-900 font-medium
                    ${disabled ? 'text-gray-500' : 'hover:border-blue-300'}
                    transition-colors duration-200
                `}
            >
                <option value="">{placeholder}</option>
                {options.map((option) => (
                    <option key={option.value} value={option.value}>
                        {option.label}
                    </option>
                ))}
            </select>

            {/* Custom chevron */}
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <ChevronDown className={`w-4 h-4 transition-colors duration-200 ${disabled ? 'text-gray-400' : 'text-gray-500 group-hover:text-blue-500'
                    }`} />
            </div>
        </div>
    )
}

// Simple Range Slider component
function RangeSlider({
    min,
    max,
    step,
    defaultMinValue,
    defaultMaxValue,
    onChange,
    formatValue
}: {
    min: number
    max: number
    step: number
    defaultMinValue: number
    defaultMaxValue: number
    onChange: (min: number, max: number) => void
    formatValue: (value: number) => string
}) {
    const [minValue, setMinValue] = useState(defaultMinValue)
    const [maxValue, setMaxValue] = useState(defaultMaxValue)
    const [editingMin, setEditingMin] = useState(false)
    const [editingMax, setEditingMax] = useState(false)
    const [tempMinValue, setTempMinValue] = useState('')
    const [tempMaxValue, setTempMaxValue] = useState('')

    // Sync with external changes
    useEffect(() => {
        setMinValue(defaultMinValue)
        setMaxValue(defaultMaxValue)
    }, [defaultMinValue, defaultMaxValue])

    const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = Math.min(Number(e.target.value), maxValue - step)
        setMinValue(value)
        onChange(value, maxValue)
    }

    const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = Math.max(Number(e.target.value), minValue + step)
        setMaxValue(value)
        onChange(minValue, value)
    }

    const startEditingMin = () => {
        setEditingMin(true)
        setTempMinValue(minValue.toString())
    }

    const startEditingMax = () => {
        setEditingMax(true)
        setTempMaxValue(maxValue.toString())
    }

    const finishEditingMin = () => {
        const value = Number(tempMinValue)
        if (!isNaN(value)) {
            const clampedValue = Math.min(Math.max(value, min), maxValue - step)
            setMinValue(clampedValue)
            onChange(clampedValue, maxValue)
        }
        setEditingMin(false)
    }

    const finishEditingMax = () => {
        const value = Number(tempMaxValue)
        if (!isNaN(value)) {
            const clampedValue = Math.max(Math.min(value, max), minValue + step)
            setMaxValue(clampedValue)
            onChange(minValue, clampedValue)
        }
        setEditingMax(false)
    }

    const handleMinKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            finishEditingMin()
        } else if (e.key === 'Escape') {
            setEditingMin(false)
        }
    }

    const handleMaxKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            finishEditingMax()
        } else if (e.key === 'Escape') {
            setEditingMax(false)
        }
    }

    // Calculate percentage positions for the range track
    const getPercent = (value: number) => ((value - min) / (max - min)) * 100

    return (
        <div className="space-y-4">
            <div className="flex justify-between">
                {editingMin ? (
                    <input
                        type="number"
                        value={tempMinValue}
                        onChange={(e) => setTempMinValue(e.target.value)}
                        onBlur={finishEditingMin}
                        onKeyDown={handleMinKeyPress}
                        className="text-sm font-medium bg-white border border-blue-500 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-600 w-32"
                        autoFocus
                        min={min}
                        max={max}
                        step={step}
                    />
                ) : (
                    <div
                        className="group flex items-center space-x-2 bg-gray-50 hover:bg-gray-100 border border-gray-200 hover:border-blue-300 rounded-lg px-3 py-2 cursor-pointer transition-all duration-200 w-32"
                        onClick={startEditingMin}
                        title="Click to edit"
                    >
                        <span className="text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors">
                            {formatValue(minValue)}
                        </span>
                        <Edit className="w-3 h-3 text-gray-400 group-hover:text-blue-500 transition-colors" />
                    </div>
                )}

                {editingMax ? (
                    <input
                        type="number"
                        value={tempMaxValue}
                        onChange={(e) => setTempMaxValue(e.target.value)}
                        onBlur={finishEditingMax}
                        onKeyDown={handleMaxKeyPress}
                        className="text-sm font-medium bg-white border border-blue-500 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-600 w-32 text-right"
                        autoFocus
                        min={min}
                        max={max}
                        step={step}
                    />
                ) : (
                    <div
                        className="group flex items-center space-x-2 bg-gray-50 hover:bg-gray-100 border border-gray-200 hover:border-blue-300 rounded-lg px-3 py-2 cursor-pointer transition-all duration-200 w-32 justify-end"
                        onClick={startEditingMax}
                        title="Click to edit"
                    >
                        <span className="text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors">
                            {formatValue(maxValue)}
                        </span>
                        <Edit className="w-3 h-3 text-gray-400 group-hover:text-blue-500 transition-colors" />
                    </div>
                )}
            </div>

            <div className="relative">
                {/* Track container */}
                <div className="relative h-6 flex items-center">
                    {/* Background track */}
                    <div className="absolute w-full h-2 bg-gray-200 rounded-full"></div>

                    {/* Active range track */}
                    <div
                        className="absolute h-2 bg-blue-500 rounded-full"
                        style={{
                            left: `${getPercent(minValue)}%`,
                            width: `${getPercent(maxValue) - getPercent(minValue)}%`
                        }}
                    ></div>
                </div>

                {/* Slider inputs container */}
                <div className="relative">
                    {/* Min slider */}
                    <input
                        type="range"
                        min={min}
                        max={max}
                        step={step}
                        value={minValue}
                        onChange={handleMinChange}
                        className="absolute w-full -top-6 h-6 bg-transparent appearance-none cursor-pointer focus:outline-none dual-range-input"
                        style={{ zIndex: minValue > max - (max - min) * 0.05 ? 2 : 1 }}
                    />

                    {/* Max slider */}
                    <input
                        type="range"
                        min={min}
                        max={max}
                        step={step}
                        value={maxValue}
                        onChange={handleMaxChange}
                        className="absolute w-full -top-6 h-6 bg-transparent appearance-none cursor-pointer focus:outline-none dual-range-input"
                        style={{ zIndex: maxValue < min + (max - min) * 0.05 ? 2 : 1 }}
                    />
                </div>
            </div>

            <style dangerouslySetInnerHTML={{
                __html: `
                    .dual-range-input {
                        pointer-events: none;
                    }
                    
                    .dual-range-input::-webkit-slider-thumb {
                        -webkit-appearance: none;
                        appearance: none;
                        height: 20px;
                        width: 20px;
                        border-radius: 50%;
                        background: #3b82f6;
                        border: 2px solid white;
                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                        cursor: pointer;
                        pointer-events: auto;
                        transition: all 0.2s ease;
                    }
                    
                    .dual-range-input::-moz-range-thumb {
                        height: 20px;
                        width: 20px;
                        border-radius: 50%;
                        background: #3b82f6;
                        border: 2px solid white;
                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                        cursor: pointer;
                        pointer-events: auto;
                        border: none;
                        transition: all 0.2s ease;
                    }
                    
                    .dual-range-input::-webkit-slider-track {
                        background: transparent;
                        height: 2px;
                    }
                    
                    .dual-range-input::-moz-range-track {
                        background: transparent;
                        height: 2px;
                        border: none;
                    }
                    
                    .dual-range-input:hover::-webkit-slider-thumb {
                        background: #2563eb;
                        transform: scale(1.1);
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                    }
                    
                    .dual-range-input:hover::-moz-range-thumb {
                        background: #2563eb;
                        transform: scale(1.1);
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                    }

                    .dual-range-input:active::-webkit-slider-thumb {
                        background: #1d4ed8;
                        transform: scale(1.15);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    }
                    
                    .dual-range-input:active::-moz-range-thumb {
                        background: #1d4ed8;
                        transform: scale(1.15);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    }

                    .dual-range-input:focus::-webkit-slider-thumb {
                        outline: none;
                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15), 0 0 0 3px rgba(59, 130, 246, 0.2);
                    }
                    
                    .dual-range-input:focus::-moz-range-thumb {
                        outline: none;
                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15), 0 0 0 3px rgba(59, 130, 246, 0.2);
                    }
                `
            }} />
        </div>
    )
}

// Saved Match Card Component
function SavedMatchCard({
    savedMatch,
    onDelete,
    onToggle,
    onUpdate,
    industries,
    states,
    formatCurrency
}: {
    savedMatch: SavedMatch
    onDelete: (id: string) => void
    onToggle: (id: string) => void
    onUpdate: (id: string, preferences: MatchPreferences) => void
    industries: Array<{ id: string; name: string }>
    states: Array<{ id: string; name: string; code: string }>
    formatCurrency: (value: number) => string
}) {
    const [isEditing, setIsEditing] = useState(false)
    const [editedPreferences, setEditedPreferences] = useState<MatchPreferences>(savedMatch.preferences)
    const [subIndustryName, setSubIndustryName] = useState<string>('Any Sub-Industry')
    const supabase = createClient()
    const [userId, setUserId] = useState<string | undefined>(undefined)

    useEffect(() => {
        const fetchUserId = async () => {
            const { data: { session } } = await supabase.auth.getSession()
            setUserId(session?.user?.id)
        }
        fetchUserId()
    }, [supabase])

    // Get industry name
    const industryName = savedMatch.preferences.industryId
        ? industries.find(i => i.id === savedMatch.preferences.industryId)?.name || 'Any Industry'
        : 'Any Industry'

    // Fetch sub-industry name when component mounts or when subIndustryId changes
    useEffect(() => {
        const fetchSubIndustryName = async () => {
            if (!savedMatch.preferences.subIndustryId) {
                setSubIndustryName('Any Sub-Industry')
                return
            }

            try {
                const { data, error } = await supabase
                    .from('sub_industries')
                    .select('name')
                    .eq('id', savedMatch.preferences.subIndustryId)
                    .single()

                if (error) {
                    console.error('Error fetching sub-industry name:', error)
                    setSubIndustryName('Any Sub-Industry')
                    return
                }

                setSubIndustryName(data?.name || 'Any Sub-Industry')
            } catch (error) {
                console.error('Error in fetchSubIndustryName:', error)
                setSubIndustryName('Any Sub-Industry')
            }
        }

        fetchSubIndustryName()
    }, [savedMatch.preferences.subIndustryId, supabase])

    // Count of matches, display "?" if loading or not loaded yet
    const matchCount = savedMatch.isLoading
        ? "..."
        : savedMatch.matches
            ? savedMatch.matches.length
            : "?"

    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 overflow-hidden">
            {/* Card Header */}
            <div className="p-6 flex justify-between items-center border-b border-gray-100">
                <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-50 rounded-lg">
                        <Target className="w-4 h-4 text-blue-600" />
                    </div>
                    <div>
                        <h3 className="font-semibold text-lg text-gray-900">{savedMatch.name}</h3>
                        <span className="text-sm text-gray-500">
                            {matchCount} {matchCount === 1 ? 'listing' : 'listings'} found
                        </span>
                    </div>
                </div>
                <div className="flex items-center space-x-2">
                    {!isEditing && (
                        <>
                            <button
                                onClick={() => setIsEditing(true)}
                                className="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                                title="Edit"
                            >
                                <Edit size={16} />
                            </button>
                            <button
                                onClick={() => onDelete(savedMatch.id)}
                                className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                                title="Delete"
                            >
                                <Trash size={16} />
                            </button>
                        </>
                    )}
                    {isEditing && (
                        <>
                            <button
                                onClick={() => {
                                    onUpdate(savedMatch.id, editedPreferences)
                                    setIsEditing(false)
                                }}
                                className="p-2 text-gray-400 hover:text-green-500 hover:bg-green-50 rounded-lg transition-colors"
                                title="Save"
                            >
                                <Save size={16} />
                            </button>
                            <button
                                onClick={() => {
                                    setEditedPreferences(savedMatch.preferences)
                                    setIsEditing(false)
                                }}
                                className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                                title="Cancel"
                            >
                                <X size={16} />
                            </button>
                        </>
                    )}
                    <button
                        onClick={() => onToggle(savedMatch.id)}
                        className="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                        title={savedMatch.isExpanded ? "Collapse" : "Expand"}
                    >
                        {savedMatch.isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                    </button>
                </div>
            </div>

            {/* Card Content (Collapsible) */}
            {savedMatch.isExpanded && (
                <div className="p-6">
                    {isEditing ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Edit Form - similar to main form but simplified */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Price Range
                                </label>
                                <RangeSlider
                                    min={0}
                                    max={1000000}
                                    step={10000}
                                    defaultMinValue={editedPreferences.priceMin}
                                    defaultMaxValue={editedPreferences.priceMax}
                                    onChange={(min, max) => setEditedPreferences({ ...editedPreferences, priceMin: min, priceMax: max })}
                                    formatValue={formatCurrency}
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Annual Revenue
                                </label>
                                <RangeSlider
                                    min={0}
                                    max={1000000}
                                    step={10000}
                                    defaultMinValue={editedPreferences.revenueMin}
                                    defaultMaxValue={editedPreferences.revenueMax}
                                    onChange={(min, max) => setEditedPreferences({ ...editedPreferences, revenueMin: min, revenueMax: max })}
                                    formatValue={formatCurrency}
                                />
                            </div>

                            <div className="md:col-span-2">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Location
                                </label>
                                <LocationAutocomplete
                                    value={editedPreferences.location || ''}
                                    onChange={(value, details) => {
                                        // Find the matching state ID if we have structured location details
                                        let stateId = null
                                        if (details?.state && states.length > 0) {
                                            const matchingState = states.find(state =>
                                                state.name.toLowerCase() === details.state?.toLowerCase() ||
                                                state.code.toLowerCase() === details.stateCode?.toLowerCase()
                                            )
                                            stateId = matchingState?.id || null
                                        }

                                        setEditedPreferences({
                                            ...editedPreferences,
                                            location: value || null,
                                            locationCity: details?.city || null,
                                            locationState: details?.state || null,
                                            locationStateId: stateId,
                                            locationCounty: details?.county || null
                                        })
                                    }}
                                    placeholder="City, State, Country"
                                    className="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                        </div>
                    ) : (
                        <>
                            {/* Summary of preferences */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <div className="bg-gray-50 rounded-lg p-4">
                                    <span className="text-sm font-medium text-gray-700">Price Range:</span>
                                    <p className="text-gray-900 font-semibold">{formatCurrency(savedMatch.preferences.priceMin)} - {formatCurrency(savedMatch.preferences.priceMax)}</p>
                                </div>
                                <div className="bg-gray-50 rounded-lg p-4">
                                    <span className="text-sm font-medium text-gray-700">Revenue Range:</span>
                                    <p className="text-gray-900 font-semibold">{formatCurrency(savedMatch.preferences.revenueMin)} - {formatCurrency(savedMatch.preferences.revenueMax)}</p>
                                </div>
                                <div className="bg-gray-50 rounded-lg p-4">
                                    <span className="text-sm font-medium text-gray-700">Industry:</span>
                                    <p className="text-gray-900 font-semibold">{industryName}</p>
                                </div>
                                <div className="bg-gray-50 rounded-lg p-4">
                                    <span className="text-sm font-medium text-gray-700">Sub-Industry:</span>
                                    <p className="text-gray-900 font-semibold">{subIndustryName}</p>
                                </div>
                                {savedMatch.preferences.location && (
                                    <div className="bg-gray-50 rounded-lg p-4">
                                        <span className="text-sm font-medium text-gray-700">Location:</span>
                                        <p className="text-gray-900 font-semibold">{savedMatch.preferences.location}</p>
                                    </div>
                                )}
                                {savedMatch.preferences.yearEstablishedMin && (
                                    <div className="bg-gray-50 rounded-lg p-4">
                                        <span className="text-sm font-medium text-gray-700">Established After:</span>
                                        <p className="text-gray-900 font-semibold">{savedMatch.preferences.yearEstablishedMin}</p>
                                    </div>
                                )}
                            </div>

                            {/* Matches */}
                            <div>
                                <h4 className="font-semibold text-lg mb-4 text-gray-900">Matching Listings</h4>
                                {savedMatch.isLoading ? (
                                    <div className="flex justify-center py-12">
                                        <div className="flex flex-col items-center">
                                            <Loader2 className="animate-spin text-gray-400 mb-3" size={32} />
                                            <p className="text-gray-500 text-sm">Finding matches...</p>
                                        </div>
                                    </div>
                                ) : savedMatch.matches && savedMatch.matches.length > 0 ? (
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        {savedMatch.matches.map(listing => (
                                            <AccessControlProvider key={listing.id} listingId={listing.id} userId={userId}>
                                                <ListingCard key={listing.id} listing={listing} />
                                            </AccessControlProvider>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="bg-gray-50 rounded-xl p-8 text-center">
                                        <div className="p-3 bg-gray-100 rounded-full mb-4 inline-block">
                                            <Search className="w-8 h-8 text-gray-400" />
                                        </div>
                                        <p className="text-gray-600 font-medium">No matches found</p>
                                        <p className="text-gray-500 text-sm mt-1">Try adjusting your search criteria</p>
                                    </div>
                                )}
                            </div>
                        </>
                    )}
                </div>
            )}
        </div>
    )
}

export function MatchClient({ industries }: MatchClientProps) {
    // Supabase client
    const supabase = createClient()

    const [userId, setUserId] = useState<string | undefined>(undefined)

    useEffect(() => {
        const fetchUserId = async () => {
            const { data: { session } } = await supabase.auth.getSession()
            setUserId(session?.user?.id)
        }
        fetchUserId()
    }, [supabase])

    // State for current preferences form
    const [preferences, setPreferences] = useState<MatchPreferences>({
        priceMin: 0,
        priceMax: 1000000,
        revenueMin: 0,
        revenueMax: 1000000,
        industryId: null,
        subIndustryId: null,
        location: null,
        locationCity: null,
        locationState: null,
        locationStateId: null,
        locationCounty: null,
        yearEstablishedMin: null
    })

    // State for saved matches
    const [savedMatches, setSavedMatches] = useState<SavedMatch[]>([])
    const [showNewMatchForm, setShowNewMatchForm] = useState(true)
    const [matchName, setMatchName] = useState('')
    const [isSaving, setIsSaving] = useState(false)

    // State for sub-industries
    const [subIndustries, setSubIndustries] = useState<Array<{ id: string; name: string; industry_id: string }>>([])

    // State for US states
    const [states, setStates] = useState<Array<{ id: string; name: string; code: string }>>([])

    // State for loading and matches
    const [isLoading, setIsLoading] = useState(false)
    const [matches, setMatches] = useState<Listing[]>([])
    const [hasSearched, setHasSearched] = useState(false)

    // Format currency for display
    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            notation: value >= 1000000 ? 'compact' : 'standard',
            maximumFractionDigits: 0
        }).format(value)
    }

    // Fetch match data based on preferences (returns promise with data)
    const fetchMatchCountData = useCallback(async (savedPreferences: MatchPreferences): Promise<Listing[]> => {
        try {
            const { priceMin, priceMax, industryId, subIndustryId } = savedPreferences

            // Get current user's ID
            const { data: { session } } = await supabase.auth.getSession()
            if (!session?.user) {
                return []
            }

            const currentUserId = session.user.id

            let query = supabase
                .from('listings')
                .select(`
                    id, 
                    title,
                    description,
                    price,
                    image_url,
                    slug,
                    user_id,
                    industries:industry_id (id, name),
                    sub_industries:sub_industry_id (id, name, industry_id),
                    listing_details!listing_id (
                        year_established,
                        location,
                        city,
                        state_id,
                        postal_code,
                        annual_revenue_ttm_min,
                        annual_revenue_ttm_max
                    ),
                    listing_anonymized_details!listing_id (
                        anonymous_title,
                        anonymous_description,
                        anonymous_image_url
                    )
                `)
                .gte('price', priceMin)
                .lte('price', priceMax)
                // Filter out user's own listings
                .neq('user_id', currentUserId)

            if (industryId) {
                query = query.eq('industry_id', industryId)
            }

            if (subIndustryId) {
                query = query.eq('sub_industry_id', subIndustryId)
            }

            const { data, error } = await query

            if (error) {
                console.error('Error fetching matches data:', error)
                return []
            }

            // Process the data and convert to the expected format
            console.log('Raw API data:', data);
            const processedData = data.map(item => {
                return {
                    id: item.id,
                    title: item.title || '',
                    description: item.description,
                    price: Number(item.price) || 0,
                    image_url: item.image_url,
                    slug: item.slug,
                    user_id: item.user_id,
                    industries: Array.isArray(item.industries) && item.industries[0]
                        ? { id: item.industries[0].id, name: item.industries[0].name }
                        : null,
                    sub_industries: Array.isArray(item.sub_industries) && item.sub_industries[0]
                        ? {
                            id: item.sub_industries[0].id,
                            name: item.sub_industries[0].name,
                            industry_id: item.sub_industries[0].industry_id
                        }
                        : null,
                    listing_details: Array.isArray(item.listing_details) && item.listing_details[0]
                        ? {
                            year_established: item.listing_details[0].year_established !== null
                                ? Number(item.listing_details[0].year_established)
                                : null,
                            location: item.listing_details[0].location,
                            city: item.listing_details[0].city,
                            state_id: item.listing_details[0].state_id,
                            postal_code: item.listing_details[0].postal_code,
                            annual_revenue_ttm_min: item.listing_details[0].annual_revenue_ttm_min !== null
                                ? Number(item.listing_details[0].annual_revenue_ttm_min)
                                : null,
                            annual_revenue_ttm_max: item.listing_details[0].annual_revenue_ttm_max !== null
                                ? Number(item.listing_details[0].annual_revenue_ttm_max)
                                : null
                        }
                        : null,
                    listing_anonymized_details: (() => {
                        const details = Array.isArray(item.listing_anonymized_details) 
                            ? item.listing_anonymized_details[0] 
                            : item.listing_anonymized_details;
                        
                        return details ? {
                            anonymous_title: details.anonymous_title,
                            anonymous_description: details.anonymous_description,
                            anonymous_image_url: details.anonymous_image_url
                        } : null;
                    })()
                } as Listing;
            });

            // Apply client-side filters
            let filteredData = processedData
            const { revenueMin, revenueMax, yearEstablishedMin, location, locationCity, locationStateId: stateId } = savedPreferences

            // Filter by revenue
            if (revenueMin > 0 || revenueMax < 1000000) {
                filteredData = filteredData.filter(listing => {
                    const revenue = listing.listing_details?.annual_revenue_ttm_max ?? listing.listing_details?.annual_revenue_ttm_min;
                    if (revenue === null || revenue === undefined) return false;
                    return revenue >= revenueMin && revenue <= revenueMax;
                });
            }

            // Filter by year established
            if (yearEstablishedMin && yearEstablishedMin > 0) {
                filteredData = filteredData.filter(listing =>
                    listing.listing_details?.year_established !== null &&
                    listing.listing_details?.year_established !== undefined &&
                    listing.listing_details.year_established >= yearEstablishedMin
                );
            }

            // Enhanced location filtering with structured data
            if (location || locationCity || stateId) {
                filteredData = filteredData.filter(listing => {
                    const details = listing.listing_details
                    if (!details) return false

                    // If we have a specific state ID, match it exactly
                    if (stateId && details.state_id === stateId) {
                        // If we also have a city, match that too (case-insensitive)
                        if (locationCity) {
                            return details.city?.toLowerCase().includes(locationCity.toLowerCase()) || false
                        }
                        return true
                    }

                    // If we have a city but no state match, try city matching
                    if (locationCity && details.city?.toLowerCase().includes(locationCity.toLowerCase())) {
                        return true
                    }

                    // Fallback to general location text search
                    if (location) {
                        const searchText = location.toLowerCase()
                        return (
                            details.location?.toLowerCase().includes(searchText) ||
                            details.city?.toLowerCase().includes(searchText) ||
                            false
                        )
                    }

                    return false
                })
            }

            console.log({ filteredData })

            return filteredData
        } catch (error) {
            console.error('Error fetching matches data:', error)
            return []
        }
    }, [supabase])

    // Define loadAllMatchCounts first
    const loadAllMatchCounts = useCallback(async (matches: SavedMatch[]) => {
        try {
            const updatedMatches = [...matches]

            // Process each match one by one
            for (let i = 0; i < matches.length; i++) {
                const match = matches[i]
                const matchData = await fetchMatchCountData(match.preferences) // This line caused the error
                updatedMatches[i] = { ...updatedMatches[i], matches: matchData }
            }

            // Update state once with all the matches
            setSavedMatches(updatedMatches)
        } catch (error) {
            console.error('Error in loadAllMatchCounts:', error)
        }
    }, [fetchMatchCountData])

    // Fetch saved matches on component mount
    const fetchSavedMatches = useCallback(async () => {
        try {
            const { data: { session } } = await supabase.auth.getSession()

            if (!session) {
                // User is not authenticated
                return
            }

            const { data, error } = await supabase
                .from('saved_matches')
                .select('*')
                .order('created_at', { ascending: false })

            if (error) {
                console.error('Error fetching saved matches:', error)
                return
            }

            // Add isExpanded property to each saved match
            const processedMatches = data.map((match) => ({
                ...match,
                isExpanded: false,
                matches: [],
                isLoading: false
            }))

            setSavedMatches(processedMatches)

            // Load match counts for each saved match (sequentially to avoid race conditions)
            if (processedMatches.length > 0) {
                loadAllMatchCounts(processedMatches)
            }
        } catch (error) {
            console.error('Error in fetchSavedMatches:', error)
        }
    }, [supabase, loadAllMatchCounts])

    useEffect(() => {
        fetchSavedMatches()
    }, [fetchSavedMatches])

    // Fetch states data
    useEffect(() => {
        const fetchStates = async () => {
            try {
                const { data, error } = await supabase
                    .from('states')
                    .select('id, name, code')
                    .order('name')

                if (error) {
                    console.error('Error fetching states:', error)
                    return
                }

                setStates(data || [])
            } catch (error) {
                console.error('Error in fetchStates:', error)
            }
        }

        fetchStates()
    }, [supabase])

    // Fetch sub-industries when industry changes
    useEffect(() => {
        if (!preferences.industryId) {
            setSubIndustries([])
            return
        }

        const fetchSubIndustries = async () => {
            const { data, error } = await supabase
                .from('sub_industries')
                .select('id, name, industry_id')
                .eq('industry_id', preferences.industryId)
                .order('name')

            if (error) {
                console.error('Error fetching sub-industries:', error)
                return
            }

            setSubIndustries(data || [])
        }

        fetchSubIndustries()
    }, [preferences.industryId, supabase])

    // Save a new match
    const saveMatch = async () => {
        try {
            if (!matchName.trim()) {
                alert('Please enter a name for your saved match')
                return
            }

            setIsSaving(true)

            const { data: { session } } = await supabase.auth.getSession()

            if (!session) {
                alert('You must be logged in to save matches')
                setIsSaving(false)
                return
            }

            const { data, error } = await supabase
                .from('saved_matches')
                .insert([
                    {
                        user_id: session.user.id,
                        name: matchName,
                        preferences
                    }
                ])
                .select()

            if (error) {
                console.error('Error saving match:', error)
                alert('Error saving match. Please try again.')
                setIsSaving(false)
                return
            }

            // Add new saved match to state with matches from current search
            const newSavedMatch = {
                ...data[0],
                isExpanded: true,
                matches: matches, // Current search results are already properly formatted
                isLoading: false
            }

            setSavedMatches(currentMatches => [newSavedMatch, ...currentMatches])
            setMatchName('')
            setShowNewMatchForm(false)
            setHasSearched(false)

            // Reset form to default values
            setPreferences({
                priceMin: 0,
                priceMax: 1000000,
                revenueMin: 0,
                revenueMax: 1000000,
                industryId: null,
                subIndustryId: null,
                location: null,
                locationCity: null,
                locationState: null,
                locationStateId: null,
                locationCounty: null,
                yearEstablishedMin: null
            })

            setIsSaving(false)
        } catch (error) {
            console.error('Error in saveMatch:', error)
            setIsSaving(false)
        }
    }

    // Delete a saved match
    const deleteMatch = async (id: string) => {
        try {
            if (!confirm('Are you sure you want to delete this saved match?')) {
                return
            }

            const { error } = await supabase
                .from('saved_matches')
                .delete()
                .eq('id', id)

            if (error) {
                console.error('Error deleting match:', error)
                alert('Error deleting match. Please try again.')
                return
            }

            // Remove from state
            setSavedMatches(currentMatches => currentMatches.filter(match => match.id !== id))
        } catch (error) {
            console.error('Error in deleteMatch:', error)
        }
    }

    // Update a saved match
    const updateMatch = async (id: string, updatedPreferences: MatchPreferences) => {
        try {
            const { error } = await supabase
                .from('saved_matches')
                .update({
                    preferences: updatedPreferences,
                    updated_at: new Date().toISOString()
                })
                .eq('id', id)

            if (error) {
                console.error('Error updating match:', error)
                alert('Error updating match. Please try again.')
                return
            }

            // Update in state
            setSavedMatches(currentMatches => currentMatches.map(match =>
                match.id === id
                    ? { ...match, preferences: updatedPreferences, isLoading: true }
                    : match
            ))

            // Refresh matches for the updated saved match
            await loadMatchesForSavedMatch(id, updatedPreferences)
        } catch (error) {
            console.error('Error in updateMatch:', error)
        }
    }

    // Toggle expansion of a saved match card
    const toggleMatchExpansion = async (id: string) => {
        const matchToToggle = savedMatches.find(match => match.id === id)

        if (!matchToToggle) return

        const newExpandedState = !matchToToggle.isExpanded

        // Update state
        setSavedMatches(currentMatches => currentMatches.map(match =>
            match.id === id ? { ...match, isExpanded: newExpandedState } : match
        ))

        // If expanding and no matches loaded yet, fetch them
        if (newExpandedState && (!matchToToggle.matches || matchToToggle.matches.length === 0)) {
            await loadMatchesForSavedMatch(id, matchToToggle.preferences)
        }
    }

    // Load matches for a saved match
    const loadMatchesForSavedMatch = async (id: string, savedPreferences: MatchPreferences) => {
        try {
            // Mark as loading
            setSavedMatches(currentMatches => currentMatches.map(match =>
                match.id === id ? { ...match, isLoading: true } : match
            ))

            // Get the data using the existing function that handles proper type conversions
            const matchData = await fetchMatchCountData(savedPreferences);

            // Update state with matches
            setSavedMatches(currentMatches => currentMatches.map(match =>
                match.id === id
                    ? { ...match, matches: matchData, isLoading: false }
                    : match
            ))
        } catch (error) {
            console.error('Error in loadMatchesForSavedMatch:', error)
            // Mark as not loading
            setSavedMatches(currentMatches => currentMatches.map(match =>
                match.id === id ? { ...match, isLoading: false } : match
            ))
        }
    }

    const handleSearch = async () => {
        setIsLoading(true)
        setHasSearched(true)

        // Use the same data fetching function for consistency
        const matchResults = await fetchMatchCountData(preferences);
        setMatches(matchResults);
        setIsLoading(false);
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header Card */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                    <div className="flex items-center space-x-3">
                        <div className="p-2 bg-purple-50 rounded-lg">
                            <Target className="w-5 h-5 text-purple-600" />
                        </div>
                        <div>
                            <h1 className="text-2xl font-semibold text-gray-900">Business Matching</h1>
                            <p className="text-gray-600 text-sm mt-1">
                                Find businesses that match your investment criteria
                            </p>
                        </div>
                    </div>
                </div>

                {/* Saved Matches Section */}
                <div className="mb-8">
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-6">
                        <div className="flex items-center space-x-3 mb-6">
                            <div className="p-2 bg-green-50 rounded-lg">
                                <Settings className="w-5 h-5 text-green-600" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Your Saved Matches</h2>
                                <p className="text-gray-600 text-sm mt-1">
                                    {savedMatches.length} saved {savedMatches.length === 1 ? 'search' : 'searches'}
                                </p>
                            </div>
                        </div>

                        {/* Saved Match Cards */}
                        {savedMatches.length > 0 ? (
                            <div className="space-y-4">
                                {savedMatches.map(match => (
                                    <SavedMatchCard
                                        key={match.id}
                                        savedMatch={match}
                                        onDelete={deleteMatch}
                                        onToggle={toggleMatchExpansion}
                                        onUpdate={updateMatch}
                                        industries={industries}
                                        states={states}
                                        formatCurrency={formatCurrency}
                                    />
                                ))}
                            </div>
                        ) : (
                            <div className="bg-gray-50 rounded-xl p-8 text-center">
                                <div className="p-4 bg-gray-100 rounded-full mb-6 inline-block">
                                    <Target className="w-12 h-12 text-gray-400" />
                                </div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Saved Matches Yet</h3>
                                <p className="text-gray-600 mb-6">
                                    Create your first match using the form below to save your search criteria and get notified of new listings.
                                </p>
                                <button
                                    onClick={() => setShowNewMatchForm(true)}
                                    className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium group"
                                >
                                    <Plus className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                                    Create Your First Match
                                </button>
                            </div>
                        )}
                    </div>
                </div>

                {/* Create New Match CTA Button - shown when form is hidden */}
                {!showNewMatchForm && (
                    <div className="mb-8">
                        <button
                            onClick={() => setShowNewMatchForm(true)}
                            className="w-full bg-blue-600 text-white rounded-xl p-6 hover:bg-blue-700 transition-colors font-medium group border-2 border-transparent hover:border-blue-300 shadow-sm"
                        >
                            <div className="flex items-center justify-center space-x-3">
                                <Plus className="w-6 h-6 group-hover:scale-110 transition-transform" />
                                <span className="text-lg">Create New Match</span>
                            </div>
                            <p className="text-blue-100 text-sm mt-2">
                                Set your criteria and find matching businesses
                            </p>
                        </button>
                    </div>
                )}

                {/* New Match Form */}
                {showNewMatchForm && (
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                        {/* Header Section */}
                        <div className="flex items-center justify-between mb-6">
                            <div className="flex items-center space-x-3">
                                <div className="p-2 bg-blue-50 rounded-lg">
                                    <Search className="w-5 h-5 text-blue-600" />
                                </div>
                                <div>
                                    <h2 className="text-xl font-semibold text-gray-900">Create New Match</h2>
                                    <p className="text-gray-600 text-sm mt-1">
                                        Set your criteria and find matching businesses
                                    </p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-2">
                                {/* Desktop Save Section */}
                                {hasSearched && (
                                    <div className="hidden sm:flex items-center space-x-2">
                                        <input
                                            type="text"
                                            placeholder="Enter a name for this match"
                                            className="border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            value={matchName}
                                            onChange={(e) => setMatchName(e.target.value)}
                                        />
                                        <button
                                            onClick={saveMatch}
                                            disabled={isSaving || !matchName.trim()}
                                            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors font-medium"
                                        >
                                            {isSaving ? (
                                                <>
                                                    <Loader2 className="inline-block mr-2 animate-spin" size={16} />
                                                    Saving...
                                                </>
                                            ) : (
                                                <>
                                                    <Save size={16} className="mr-2" />
                                                    Save
                                                </>
                                            )}
                                        </button>
                                    </div>
                                )}
                                <button
                                    onClick={() => setShowNewMatchForm(false)}
                                    className="flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                                    title="Hide form"
                                >
                                    <X size={18} />
                                </button>
                            </div>
                        </div>

                        {/* Mobile Save Match Section - Under title with dividers */}
                        {hasSearched && (
                            <div className="block sm:hidden">
                                <div className="border-t border-gray-200 mb-6"></div>
                                <div className="mb-6 space-y-3">
                                    <input
                                        type="text"
                                        placeholder="Enter a name for this match"
                                        className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-base"
                                        value={matchName}
                                        onChange={(e) => setMatchName(e.target.value)}
                                    />
                                    <button
                                        onClick={saveMatch}
                                        disabled={isSaving || !matchName.trim()}
                                        className="w-full flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors font-medium"
                                    >
                                        {isSaving ? (
                                            <>
                                                <Loader2 className="inline-block mr-2 animate-spin" size={16} />
                                                Saving...
                                            </>
                                        ) : (
                                            <>
                                                <Save size={16} className="mr-2" />
                                                Save Match
                                            </>
                                        )}
                                    </button>
                                </div>
                                <div className="border-t border-gray-200 mb-6"></div>
                            </div>
                        )}

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Price Range */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Price Range
                                </label>
                                <RangeSlider
                                    min={0}
                                    max={1000000}
                                    step={10000}
                                    defaultMinValue={preferences.priceMin}
                                    defaultMaxValue={preferences.priceMax}
                                    onChange={(min, max) => setPreferences({ ...preferences, priceMin: min, priceMax: max })}
                                    formatValue={formatCurrency}
                                />
                            </div>

                            {/* Revenue Range */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Annual Revenue
                                </label>
                                <RangeSlider
                                    min={0}
                                    max={1000000}
                                    step={10000}
                                    defaultMinValue={preferences.revenueMin}
                                    defaultMaxValue={preferences.revenueMax}
                                    onChange={(min, max) => setPreferences({ ...preferences, revenueMin: min, revenueMax: max })}
                                    formatValue={formatCurrency}
                                />
                            </div>

                            {/* Industry */}
                            <div>
                                <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-2">
                                    Industry
                                </label>
                                <CustomSelect
                                    value={preferences.industryId || ''}
                                    onChange={(value) => setPreferences({
                                        ...preferences,
                                        industryId: value || null,
                                        subIndustryId: null
                                    })}
                                    options={industries.map((industry) => ({
                                        value: industry.id,
                                        label: industry.name
                                    }))}
                                    placeholder="Select industry"
                                />
                            </div>

                            {/* Sub-Industry */}
                            <div>
                                <label htmlFor="subIndustry" className="block text-sm font-medium text-gray-700 mb-2">
                                    Sub-Industry
                                </label>
                                <CustomSelect
                                    value={preferences.subIndustryId || ''}
                                    onChange={(value) => setPreferences({ ...preferences, subIndustryId: value || null })}
                                    options={subIndustries.map((subIndustry) => ({
                                        value: subIndustry.id,
                                        label: subIndustry.name
                                    }))}
                                    placeholder="Select sub-industry"
                                    disabled={!preferences.industryId || subIndustries.length === 0}
                                />
                            </div>

                            {/* Location */}
                            <div>
                                <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                                    Location
                                </label>
                                <LocationAutocomplete
                                    value={preferences.location || ''}
                                    onChange={(value, details) => {
                                        // Find the matching state ID if we have structured location details
                                        let stateId = null
                                        if (details?.state && states.length > 0) {
                                            const matchingState = states.find(state =>
                                                state.name.toLowerCase() === details.state?.toLowerCase() ||
                                                state.code.toLowerCase() === details.stateCode?.toLowerCase()
                                            )
                                            stateId = matchingState?.id || null
                                        }

                                        setPreferences({
                                            ...preferences,
                                            location: value || null,
                                            locationCity: details?.city || null,
                                            locationState: details?.state || null,
                                            locationStateId: stateId,
                                            locationCounty: details?.county || null
                                        })
                                    }}
                                    placeholder="City, State, Country"
                                    className="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>

                            {/* Year Established */}
                            <div>
                                <label htmlFor="yearEstablished" className="block text-sm font-medium text-gray-700 mb-2">
                                    Minimum Year Established
                                </label>
                                <CustomSelect
                                    value={preferences.yearEstablishedMin?.toString() || ''}
                                    onChange={(value) => setPreferences({
                                        ...preferences,
                                        yearEstablishedMin: value ? parseInt(value) : null
                                    })}
                                    options={Array.from({ length: 30 }, (_, i) => {
                                        const year = new Date().getFullYear() - i;
                                        return {
                                            value: year.toString(),
                                            label: year.toString()
                                        };
                                    })}
                                    placeholder="Select year"
                                />
                            </div>
                        </div>

                        <div className="mt-8">
                            <button
                                className="w-full sm:w-auto inline-flex items-center justify-center px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:bg-gray-400"
                                onClick={handleSearch}
                                disabled={isLoading}
                            >
                                {isLoading ? (
                                    <>
                                        <Loader2 className="inline-block mr-2 animate-spin" size={18} />
                                        Finding Matches...
                                    </>
                                ) : (
                                    <>
                                        <Search className="w-5 h-5 mr-2" />
                                        Find Matches
                                    </>
                                )}
                            </button>
                        </div>
                    </div>
                )}

                {/* Results Section */}
                {hasSearched && (
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                        <div className="flex items-center space-x-3 mb-6">
                            <div className="p-2 bg-orange-50 rounded-lg">
                                <TrendingUp className="w-5 h-5 text-orange-600" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">
                                    {isLoading ? 'Finding Matches...' : `${matches.length} Matching Businesses`}
                                </h2>
                                <p className="text-gray-600 text-sm mt-1">
                                    {isLoading ? 'Please wait while we search...' : 'Based on your search criteria'}
                                </p>
                            </div>
                        </div>

                        {!isLoading && matches.length === 0 && (
                            <div className="bg-gray-50 rounded-xl p-12 text-center">
                                <div className="p-4 bg-gray-100 rounded-full mb-6 inline-block">
                                    <Search className="w-12 h-12 text-gray-400" />
                                </div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Matches Found</h3>
                                <p className="text-gray-600 mb-6">
                                    No businesses match your current criteria. Try adjusting your preferences to see more results.
                                </p>
                                <button
                                    onClick={() => {
                                        setPreferences({
                                            priceMin: 0,
                                            priceMax: 1000000,
                                            revenueMin: 0,
                                            revenueMax: 1000000,
                                            industryId: null,
                                            subIndustryId: null,
                                            location: null,
                                            locationCity: null,
                                            locationState: null,
                                            locationStateId: null,
                                            locationCounty: null,
                                            yearEstablishedMin: null
                                        })
                                        setHasSearched(false)
                                    }}
                                    className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium group"
                                >
                                    <Settings className="w-5 h-5 mr-2 group-hover:rotate-90 transition-transform" />
                                    Reset Criteria
                                </button>
                            </div>
                        )}

                        {matches.length > 0 && (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {matches.map(listing => (
                                    <AccessControlProvider key={listing.id} listingId={listing.id} userId={userId}>
                                        <ListingCard key={listing.id} listing={listing} />
                                    </AccessControlProvider>
                                ))}
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    )
} 