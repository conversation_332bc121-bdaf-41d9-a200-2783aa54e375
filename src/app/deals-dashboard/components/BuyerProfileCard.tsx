'use client';

import { User, Mail, Building2 } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { NewMessageModal } from '@/components';
import { useAccessControlledDisplay } from '@/hooks/useAccessControlledDisplay';

interface BuyerProfileCardProps {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string | null;
    company: string | null;
    profilePhoto: string | null;
    listingId: string;
    lastMessageDate: string;
}

export default function BuyerProfileCard({
    id,
    firstName,
    lastName,
    email,
    company,
    profilePhoto,
    listingId,
    lastMessageDate
}: BuyerProfileCardProps) {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
    const [listingData, setListingData] = useState<{
        title: string;
        anonymousTitle?: string | null;
    } | null>(null);
    const supabase = createClient();

    const fullName = `${firstName || ''} ${lastName || ''}`.trim() || 'Anonymous Buyer';

    // Use access-controlled display for listing title
    const { displayTitle } = useAccessControlledDisplay({
        realTitle: listingData?.title,
        anonymizedDetails: { anonymous_title: listingData?.anonymousTitle }
    });

    // Fetch listing title and anonymized details for the modal
    useEffect(() => {
        const fetchListingData = async () => {
            const { data: listing } = await supabase
                .from('listings')
                .select(`
                    title,
                    listing_anonymized_details (
                        anonymous_title
                    )
                `)
                .eq('id', listingId)
                .single();

            if (listing) {
                const anonymizedDetails = Array.isArray(listing.listing_anonymized_details) 
                    ? listing.listing_anonymized_details[0] 
                    : listing.listing_anonymized_details;
                    
                setListingData({
                    title: listing.title,
                    anonymousTitle: anonymizedDetails?.anonymous_title || null
                });
            }
        };

        fetchListingData();
    }, [listingId, supabase]);

    const handleNewMessage = async () => {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.user?.id) return;

        // Create conversation if it doesn't exist
        const participants = [session.user.id, id].sort();
        const { data: existingConv } = await supabase
            .from('conversations')
            .select('id')
            .eq('listing_id', listingId)
            .eq('participant1_id', participants[0])
            .eq('participant2_id', participants[1])
            .single();

        let conversationId;

        if (!existingConv) {
            const { data: newConv } = await supabase
                .from('conversations')
                .insert({
                    listing_id: listingId,
                    participant1_id: participants[0],
                    participant2_id: participants[1]
                })
                .select('id')
                .single();

            conversationId = newConv?.id;
        } else {
            conversationId = existingConv.id;
        }

        setIsModalOpen(true);
        setCurrentConversationId(conversationId);
    };

    return (
        <>
            <div className="bg-white rounded-xl border border-gray-200/60 shadow-sm overflow-hidden hover:shadow-lg transition-all duration-300 group">
                {/* Profile Photo Section */}
                <div className="relative h-32 w-full overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-100">
                    {profilePhoto ? (
                        <Image
                            src={profilePhoto}
                            alt={fullName}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-500"
                        />
                    ) : (
                        <div className="flex items-center justify-center h-full">
                            <div className="w-16 h-16 bg-white rounded-full shadow-md flex items-center justify-center">
                                <User className="w-8 h-8 text-gray-400" />
                            </div>
                        </div>
                    )}

                    {/* Dark gradient overlay for text readability */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-black/10 to-transparent"></div>
                </div>

                {/* Content Section */}
                <div className="p-5">
                    <h3 className="font-semibold text-gray-900 mb-2 text-base leading-tight">{fullName}</h3>

                    {/* Email */}
                    {email && (
                        <div className="flex items-center gap-2 mb-2 text-sm text-gray-600">
                            <Mail className="w-4 h-4" />
                            <span className="truncate">{email}</span>
                        </div>
                    )}

                    {/* Company */}
                    {company && (
                        <div className="flex items-center gap-2 mb-3 text-sm text-gray-600">
                            <Building2 className="w-4 h-4" />
                            <span className="truncate">{company}</span>
                        </div>
                    )}

                    {/* Last Contact Date */}
                    <div className="text-xs text-gray-500 mb-4">
                        Last contact: {new Date(lastMessageDate).toLocaleDateString()}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2.5">
                        <button
                            onClick={handleNewMessage}
                            className="flex items-center justify-center gap-2 flex-1 px-4 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 group/btn shadow-sm hover:shadow-md"
                        >
                            <Mail className="w-4 h-4 group-hover/btn:scale-110 transition-transform duration-200" />
                            <span>Message</span>
                        </button>
                        <Link
                            href={`/profile/${id}`}
                            className="flex items-center justify-center gap-2 flex-1 px-4 py-2.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm font-medium transition-all duration-200 group/btn border border-gray-200 hover:border-gray-300"
                        >
                            <User className="w-4 h-4 group-hover/btn:scale-110 transition-transform duration-200" />
                            <span>Profile</span>
                        </Link>
                    </div>
                </div>
            </div>

            <NewMessageModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                recipientId={id}
                listingId={listingId}
                ownerName={{
                    firstName: firstName || '',
                    lastName: lastName || ''
                }}
                ownerAvatar={profilePhoto}
                listingName={displayTitle}
                conversationId={currentConversationId || undefined}
            />
        </>
    );
} 