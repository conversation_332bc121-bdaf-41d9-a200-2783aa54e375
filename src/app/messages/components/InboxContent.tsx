'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import { ChatUser, Profile, FormattedMessage, MessageAttachment } from '@/types/inbox';
import { User } from '@supabase/supabase-js';
import { MessageSquare, User as UserIcon, Download, Upload, ChevronDown, FileText, Image as ImageIcon, File, Loader2, Check, FolderLock, Lock, Unlock, ChevronUp } from 'lucide-react';
import Image from 'next/image';
import * as RadixSwitch from '@radix-ui/react-switch';
import { toast } from 'sonner';
import { sendCourierMessageNotification, sendCourierDataRoomAccessNotification } from '@/lib/courier-helpers';

interface MessageGroup {
    listingId: string;
    otherParticipantId: string;
    messages: FormattedMessage[];
    listing: {
        id: string;
        title: string;
        image_url: string;
        user_id: string;
    };
}

interface ListingInfo {
    id: string;
    title: string;
    image_url: string;
    user_id: string;
}

interface UserListProps {
    users: ChatUser[];
    isLoading: boolean;
    selectedUser: Profile | null;
    onUserSelect: (profile: Profile, listingId: string) => void;
}

interface MessageListProps {
    messages: FormattedMessage[];
    selectedUser: Profile | null;
    isLoading: boolean;
    currentUser: User | null;
    currentUserProfile: Profile | null;
    listingInfo: ListingInfo | null;
    onSendMessage: (message: string) => void;
    onFileUpload: (file: File, type: 'file' | 'image' | 'letter_of_intent') => void;
    isUploading: boolean;
    uploadComplete: boolean;
    // Mobile conversation switcher props
    chatUsers: ChatUser[];
    isMobileAccordionOpen: boolean;
    onToggleMobileAccordion: () => void;
    onUserSelect: (profile: Profile, listingId: string) => void;
}

interface RealtimeMessagePayload {
    id: string;
    content: string;
    created_at: string;
    sender_id: string;
    recipient_id: string;
    listing_id: string;
    read: boolean;
    conversation_id: string | null;
}

// Simple inline components
const EmptyState = ({ onNewMessage }: { onNewMessage: () => void }) => (
    <div className="flex flex-col items-center justify-center h-96 text-center">
        <MessageSquare className="w-16 h-16 text-gray-300 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No conversations yet</h3>
        <p className="text-gray-500 mb-4">Browse businesses and start a conversation with an owner</p>
        <button
            onClick={onNewMessage}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
            Browse Businesses
        </button>
    </div>
);

// LOI Download Component
const LOIDownloadButton = ({ isVisible }: { isVisible: boolean }) => {
    const handleDownload = () => {
        // Download the LOI template from public folder
        const link = document.createElement('a');
        link.href = '/docs/LOI_Template.docx';
        link.download = 'LOI_Template.docx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    if (!isVisible) return null;

    return (
        <div className="mb-4 p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                        <FileText className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                        <h4 className="text-sm font-medium text-green-900">Tools</h4>
                        <p className="text-xs text-green-700">
                            Download Letter of Intent template
                        </p>
                    </div>
                </div>
                <button
                    onClick={handleDownload}
                    className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                >
                    <Download className="w-3 h-3" />
                    <span>Download LOI</span>
                </button>
            </div>
        </div>
    );
};

// Upload Dropdown Component
const UploadDropdown = ({
    onFileUpload,
    isUploading = false,
    uploadComplete = false,
    showLOIOption = false
}: {
    onFileUpload: (file: File, type: 'file' | 'image' | 'letter_of_intent') => void;
    isUploading?: boolean;
    uploadComplete?: boolean;
    showLOIOption?: boolean;
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [selectedUploadType, setSelectedUploadType] = useState<'file' | 'image' | 'letter_of_intent'>('file');

    const handleFileSelect = (type: 'file' | 'image' | 'letter_of_intent') => {
        setSelectedUploadType(type);
        setIsOpen(false);
        if (fileInputRef.current) {
            // Set accept attribute based on type
            if (type === 'image') {
                fileInputRef.current.accept = 'image/*';
            } else if (type === 'letter_of_intent') {
                fileInputRef.current.accept = '.pdf,.docx,.doc,.rtf';
            } else {
                fileInputRef.current.accept = '.pdf,.docx,.doc,.xlsx,.xls,.pptx,.ppt,.txt,.csv,.rtf,.zip';
            }
            fileInputRef.current.click();
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            onFileUpload(file, selectedUploadType);
        }
        // Reset the input
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    // Show different button content based on state
    const getButtonContent = () => {
        if (isUploading) {
            return (
                <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>Uploading...</span>
                </>
            );
        }

        if (uploadComplete) {
            return (
                <>
                    <Check className="w-4 h-4 text-green-600" />
                    <span>Uploaded!</span>
                </>
            );
        }

        return (
            <>
                <Upload className="w-4 h-4" />
                <span>Upload</span>
                <ChevronDown className="w-4 h-4" />
            </>
        );
    };

    const getButtonStyles = () => {
        if (uploadComplete) {
            return "flex items-center space-x-2 px-4 py-3 bg-green-100 text-green-700 rounded-lg transition-colors";
        }

        if (isUploading) {
            return "flex items-center space-x-2 px-4 py-3 bg-blue-100 text-blue-700 rounded-lg transition-colors";
        }

        return "flex items-center space-x-2 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors";
    };

    return (
        <div className="relative">
            <button
                onClick={() => !isUploading && !uploadComplete && setIsOpen(!isOpen)}
                disabled={isUploading}
                className={getButtonStyles()}
            >
                {getButtonContent()}
            </button>

            {isOpen && !isUploading && !uploadComplete && (
                <div className="absolute bottom-full mb-2 left-0 bg-white rounded-lg shadow-lg border border-gray-200 z-10 min-w-[200px]">
                    <div className="py-2">
                        <button
                            onClick={() => handleFileSelect('file')}
                            className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                        >
                            <File className="w-4 h-4" />
                            <span>File</span>
                        </button>
                        <button
                            onClick={() => handleFileSelect('image')}
                            className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                        >
                            <ImageIcon className="w-4 h-4" />
                            <span>Image</span>
                        </button>
                        {showLOIOption && (
                            <button
                                onClick={() => handleFileSelect('letter_of_intent')}
                                className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                            >
                                <FileText className="w-4 h-4" />
                                <span>Letter of Intent</span>
                            </button>
                        )}
                    </div>
                </div>
            )}

            <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                onChange={handleFileChange}
                disabled={isUploading}
            />
        </div>
    );
};

// Message Attachment Display Component
const MessageAttachmentDisplay = ({
    attachment,
    currentUser,
    listingInfo,
    senderId
}: {
    attachment: MessageAttachment;
    currentUser: User | null;
    listingInfo: ListingInfo | null;
    senderId: string;
}) => {
    const getAttachmentIcon = () => {
        switch (attachment.attachment_type) {
            case 'image':
                return <ImageIcon className="w-4 h-4" />;
            case 'letter_of_intent':
                return <FileText className="w-4 h-4 text-green-600" />;
            default:
                return <File className="w-4 h-4" />;
        }
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const handleDownload = async () => {
        // If this is the listing owner downloading an LOI, update deal status
        if (attachment.attachment_type === 'letter_of_intent' &&
            currentUser &&
            listingInfo &&
            listingInfo.user_id === currentUser.id) {
            try {
                const { createClient } = await import('@/utils/supabase/client');
                const supabase = createClient();

                // Update deal status to indicate LOI has been downloaded
                const { error } = await supabase
                    .from('deal_statuses')
                    .update({
                        status: 'letter_of_intent',
                        status_changed_at: new Date().toISOString()
                    })
                    .eq('listing_id', listingInfo.id)
                    .eq('buyer_id', senderId);

                if (error) {
                    console.error('Error updating deal status for LOI download:', error);
                } else {
                    console.log('✅ Deal status updated to letter_of_intent');
                }
            } catch (error) {
                console.error('Error tracking LOI download:', error);
            }
        }

        // Open the file in a new tab
        window.open(attachment.file_url, '_blank');
    };

    return (
        <div className="mt-2 p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-center space-x-3">
                {getAttachmentIcon()}
                <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                        {attachment.file_name}
                    </p>
                    <p className="text-xs text-gray-500">
                        {formatFileSize(attachment.file_size)}
                    </p>
                </div>
                <button
                    onClick={handleDownload}
                    className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700 transition-colors"
                >
                    <Download className="w-3 h-3" />
                    <span>Download</span>
                </button>
            </div>
        </div>
    );
};

// System Notification Component for special events like LOI sharing
const SystemNotification = ({
    message,
    attachment,
    timestamp,
    currentUser,
    listingInfo
}: {
    message: FormattedMessage;
    attachment: MessageAttachment;
    timestamp: string;
    currentUser: User | null;
    listingInfo: ListingInfo | null;
}) => {
    const senderName = message.sender?.first_name || 'Someone';
    const isListingOwner = currentUser && listingInfo && listingInfo.user_id === currentUser.id;

    const handleLoiDownload = async () => {
        // If this is the listing owner downloading the LOI, update deal status
        if (isListingOwner && attachment.attachment_type === 'letter_of_intent') {
            try {
                const { createClient } = await import('@/utils/supabase/client');
                const supabase = createClient();

                // Update deal status to indicate LOI has been downloaded
                const { error } = await supabase
                    .from('deal_statuses')
                    .update({
                        status: 'letter_of_intent',
                        status_changed_at: new Date().toISOString()
                    })
                    .eq('listing_id', listingInfo.id)
                    .eq('buyer_id', message.sender_id);

                if (error) {
                    console.error('Error updating deal status for LOI download:', error);
                } else {
                    console.log('✅ Deal status updated to letter_of_intent');
                }
            } catch (error) {
                console.error('Error tracking LOI download:', error);
            }
        }

        // Open the file in a new tab
        window.open(attachment.file_url, '_blank');
    };

    return (
        <div className="w-full px-4 my-8">
            <div className="bg-gray-50 rounded-xl p-6 border border-gray-200 shadow-sm">
                {/* Desktop Layout */}
                <div className="hidden md:flex items-center space-x-6">
                    {/* Left Column - Icon */}
                    <div className="flex-shrink-0">
                        <div className="p-2 bg-green-100 rounded-full">
                            <FileText className="w-5 h-5 text-green-600" />
                        </div>
                    </div>

                    {/* Middle Column - Title and Description */}
                    <div className="flex-1">
                        <h4 className="text-base font-semibold text-gray-900 mb-1">
                            Letter of Intent Shared
                        </h4>
                        <p className="text-sm text-gray-600">
                            {senderName} shared a Letter of Intent ({attachment.file_name})
                        </p>
                    </div>

                    {/* Right Column - Download Section */}
                    <div className="flex-shrink-0">
                        <div className="flex items-center space-x-3">
                            <p className="text-xs text-gray-500">
                                {(attachment.file_size / 1024).toFixed(1)} KB
                            </p>

                            <button
                                onClick={handleLoiDownload}
                                className="flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded text-xs font-medium hover:bg-green-700 transition-colors whitespace-nowrap"
                            >
                                <Download className="w-3 h-3" />
                                <span>Download</span>
                            </button>
                        </div>
                    </div>
                </div>

                {/* Mobile Layout */}
                <div
                    className="md:hidden cursor-pointer hover:bg-gray-100 transition-colors rounded-lg p-1 -m-1"
                    onClick={handleLoiDownload}
                >
                    <div className="flex items-start justify-between">
                        {/* Left - Icon, Title and Description */}
                        <div className="flex items-start space-x-4 flex-1">
                            <div className="flex-shrink-0">
                                <div className="p-2 bg-green-100 rounded-full">
                                    <FileText className="w-5 h-5 text-green-600" />
                                </div>
                            </div>
                            <div className="flex-1 min-w-0">
                                <h4 className="text-base font-semibold text-gray-900 mb-1">
                                    Letter of Intent Shared
                                </h4>
                                <p className="text-sm text-gray-600 mb-1">
                                    {senderName} shared a Letter of Intent
                                </p>
                                <p className="text-xs text-gray-500 truncate">
                                    {attachment.file_name} | {(attachment.file_size / 1024).toFixed(1)} KB
                                </p>
                            </div>
                        </div>

                        {/* Right - Download Icon */}
                        <div className="flex-shrink-0 ml-2">
                            <div className="p-2 bg-green-600 rounded-full">
                                <Download className="w-4 h-4 text-white" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Timestamp - Outside card, centered below */}
            <p className="text-xs text-gray-400 mt-3 text-center">
                {timestamp}
            </p>
        </div>
    );
};

// DataRoom Access Toggle Component
const DataRoomAccessToggle = ({
    listingInfo,
    currentUser,
    selectedUser,
    hasDataRoomAccess,
    onAccessChange
}: {
    listingInfo: ListingInfo | null;
    currentUser: User | null;
    selectedUser: Profile | null;
    hasDataRoomAccess: boolean;
    onAccessChange: (hasAccess: boolean) => void;
}) => {
    const [isLoading, setIsLoading] = useState(false);
    const supabase = createClient();

    // Only show if current user is the listing owner
    const isListingOwner = currentUser && listingInfo && listingInfo.user_id === currentUser.id;

    if (!isListingOwner || !selectedUser || !listingInfo) return null;

    const handleToggleAccess = async (checked: boolean) => {
        if (!selectedUser || !listingInfo || !currentUser) return;

        setIsLoading(true);

        try {
            if (checked) {
                // Grant access
                const { error: accessError } = await supabase
                    .from('data_room_access')
                    .insert({
                        listing_id: listingInfo.id,
                        user_id: selectedUser.user_id,
                        granted_by: currentUser.id
                    });

                if (accessError) {
                    // Check if error is due to duplicate entry
                    if (accessError.code === '23505') {
                        toast.info('User already has DataRoom access');
                        onAccessChange(true);
                        return;
                    }
                    throw accessError;
                }

                // Create notification
                const { error: notificationError } = await supabase
                    .from('data_room_access_notifications')
                    .insert({
                        user_id: selectedUser.user_id,
                        listing_id: listingInfo.id,
                        granted_by: currentUser.id
                    });

                if (notificationError) {
                    console.warn('Failed to create notification:', notificationError);
                }

                // Send Courier notification (non-blocking)
                try {
                    await sendCourierDataRoomAccessNotification({
                        recipientUserId: selectedUser.user_id,
                        listingId: listingInfo.id,
                    });
                    console.log('📱 Courier data room access notification sent');
                } catch (courierError) {
                    console.log('📱 Courier data room access notification failed (non-critical):', courierError);
                }

                toast.success(`DataRoom access granted to ${(selectedUser.first_name || selectedUser.last_name)
                    ? `${selectedUser.first_name || ''} ${selectedUser.last_name || ''}`.trim()
                    : 'User'}`);
                onAccessChange(true);
            } else {
                // Revoke access
                const { error: revokeError } = await supabase
                    .from('data_room_access')
                    .delete()
                    .eq('listing_id', listingInfo.id)
                    .eq('user_id', selectedUser.user_id);

                if (revokeError) throw revokeError;

                toast.success(`DataRoom access revoked from ${(selectedUser.first_name || selectedUser.last_name)
                    ? `${selectedUser.first_name || ''} ${selectedUser.last_name || ''}`.trim()
                    : 'User'}`);
                onAccessChange(false);
            }
        } catch (error) {
            console.error('Error toggling DataRoom access:', error);
            const errorMessage = error instanceof Error ? error.message : 'Please try again.';
            toast.error(`Failed to update DataRoom access: ${errorMessage}`);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                        <FolderLock className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                        <h4 className="text-sm font-medium text-blue-900">DataRoom Access</h4>
                        <p className="text-xs text-blue-700">
                            {hasDataRoomAccess
                                ? `${(selectedUser.first_name || selectedUser.last_name)
                                    ? `${selectedUser.first_name || ''} ${selectedUser.last_name || ''}`.trim()
                                    : 'User'} has access to private documents`
                                : `Grant ${(selectedUser.first_name || selectedUser.last_name)
                                    ? `${selectedUser.first_name || ''} ${selectedUser.last_name || ''}`.trim()
                                    : 'user'} access to private documents`
                            }
                        </p>
                    </div>
                </div>

                <div className="flex items-center space-x-2">
                    <span className={`text-xs font-medium ${hasDataRoomAccess ? 'text-blue-600' : 'text-gray-500'}`}>
                        {hasDataRoomAccess ? <Unlock className="inline h-3 w-3 mr-1" /> : <Lock className="inline h-3 w-3 mr-1" />}
                        <span className="hidden md:inline">{hasDataRoomAccess ? 'Granted' : 'Restricted'}</span>
                    </span>

                    <RadixSwitch.Root
                        checked={hasDataRoomAccess}
                        onCheckedChange={handleToggleAccess}
                        disabled={isLoading}
                        className="group relative inline-flex h-[20px] w-[36px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-blue-600 data-[state=unchecked]:bg-gray-300"
                    >
                        <RadixSwitch.Thumb className="pointer-events-none block h-4 w-4 rounded-full bg-white shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0" />
                    </RadixSwitch.Root>
                </div>
            </div>
        </div>
    );
};

// Updated UserList component with mobile accordion functionality
const UserList = ({ users, isLoading, selectedUser, onUserSelect }: UserListProps) => {
    return (
        <>
            {/* Desktop Layout */}
            <div className="hidden md:block w-80 border-r border-gray-200 bg-gray-50 overflow-y-auto">
                <div className="p-6 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-900">Messages</h2>
                </div>
                {isLoading ? (
                    <div className="p-6">Loading...</div>
                ) : (
                    <div className="divide-y divide-gray-200">
                        {users.map((user: ChatUser) => (
                            <div
                                key={`${user.profile.user_id}-${user.listingId}`}
                                onClick={() => onUserSelect(user.profile, user.listingId)}
                                className={`p-4 cursor-pointer hover:bg-white transition-colors ${selectedUser?.user_id === user.profile.user_id ? 'bg-white border-r-2 border-blue-500' : ''
                                    }`}
                            >
                                <div className="flex items-start space-x-3">
                                    <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                                        {user.profile.profile_photo ? (
                                            <Image
                                                src={user.profile.profile_photo}
                                                alt={`${user.profile.first_name || ''} ${user.profile.last_name || ''}`.trim() || 'User'}
                                                width={40}
                                                height={40}
                                                className="w-full h-full object-cover"
                                            />
                                        ) : (
                                            <div className="w-full h-full flex items-center justify-center">
                                                <UserIcon className="w-6 h-6 text-gray-600" />
                                            </div>
                                        )}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <p className="text-sm font-medium text-gray-900">
                                            {(user.profile.first_name || user.profile.last_name)
                                                ? `${user.profile.first_name || ''} ${user.profile.last_name || ''}`.trim()
                                                : 'Unknown User'}
                                        </p>
                                        <p className="text-xs text-gray-500 truncate">
                                            {user.lastMessage.listing?.title}
                                        </p>
                                        <p className="text-xs text-gray-400 truncate">
                                            {user.lastMessage.content}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* Mobile Layout - Hidden, conversation switcher moved to MessageList */}
            <div className="md:hidden hidden">
            </div>
        </>
    );
};

const MessageList = ({
    messages,
    selectedUser,
    isLoading,
    currentUser,
    currentUserProfile,
    listingInfo,
    onSendMessage,
    onFileUpload,
    isUploading,
    uploadComplete,
    chatUsers,
    isMobileAccordionOpen,
    onToggleMobileAccordion,
    onUserSelect
}: MessageListProps) => {
    const [newMessage, setNewMessage] = useState('');
    const [messageAttachments, setMessageAttachments] = useState<Record<string, MessageAttachment[]>>({});
    const [hasDataRoomAccess, setHasDataRoomAccess] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const supabase = createClient();

    // Get listing information from the first message
    const otherUserName = selectedUser 
        ? (selectedUser.first_name || selectedUser.last_name)
            ? `${selectedUser.first_name || ''} ${selectedUser.last_name || ''}`.trim()
            : 'Unknown User'
        : 'Unknown User';
    const currentUserName = currentUser ? `You` : 'Unknown';

    // Check if current user is NOT the listing owner (potential buyer)
    const isPotentialBuyer = currentUser && listingInfo && listingInfo.user_id !== currentUser.id;

    // Check DataRoom access when listing or selected user changes
    useEffect(() => {
        const checkDataRoomAccess = async () => {
            if (!selectedUser || !listingInfo || !currentUser) {
                setHasDataRoomAccess(false);
                return;
            }

            try {
                const { data: accessData } = await supabase
                    .from('data_room_access')
                    .select('id')
                    .eq('listing_id', listingInfo.id)
                    .eq('user_id', selectedUser.user_id)
                    .single();

                setHasDataRoomAccess(!!accessData);
            } catch {
                // No access found
                setHasDataRoomAccess(false);
            }
        };

        checkDataRoomAccess();
    }, [selectedUser, listingInfo, currentUser, supabase]);

    const handleSendMessage = () => {
        if (newMessage.trim()) {
            onSendMessage(newMessage);
            setNewMessage('');
        }
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    // Auto-scroll to bottom when messages change
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    // Fetch attachments for messages
    useEffect(() => {
        const fetchAttachments = async () => {
            if (!messages.length) return;

            try {
                const { createClient } = await import('@/utils/supabase/client');
                const supabase = createClient();

                const messageIds = messages.map(m => m.id);
                const { data: attachments } = await supabase
                    .from('message_attachments')
                    .select('*')
                    .in('message_id', messageIds);

                if (attachments) {
                    const attachmentsByMessage: Record<string, MessageAttachment[]> = {};
                    attachments.forEach(attachment => {
                        if (!attachmentsByMessage[attachment.message_id]) {
                            attachmentsByMessage[attachment.message_id] = [];
                        }
                        attachmentsByMessage[attachment.message_id].push(attachment);
                    });
                    setMessageAttachments(attachmentsByMessage);
                }
            } catch (error) {
                console.error('Error fetching attachments:', error);
            }
        };

        fetchAttachments();
    }, [messages]);

    return (
        <div className="flex flex-col h-full">
            {/* Mobile Conversation Switcher - Sticky within message content */}
            <div className="md:hidden">
                <div className="sticky top-0 z-50 mx-2 mt-2 mb-3 bg-gray-50 rounded-lg border border-gray-200 shadow-lg overflow-hidden backdrop-blur-sm">
                    {/* Currently Active Conversation Header - Always Visible */}
                    {selectedUser && (
                        <div className="p-3 bg-blue-50 border-b border-blue-200">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3 flex-1 min-w-0">
                                    <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                                        {selectedUser.profile_photo ? (
                                            <Image
                                                src={selectedUser.profile_photo}
                                                alt={`${selectedUser.first_name || ''} ${selectedUser.last_name || ''}`.trim() || 'User'}
                                                width={32}
                                                height={32}
                                                className="w-full h-full object-cover"
                                            />
                                        ) : (
                                            <div className="w-full h-full flex items-center justify-center">
                                                <UserIcon className="w-4 h-4 text-gray-600" />
                                            </div>
                                        )}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <p className="text-sm font-medium text-blue-900">
                                            {(selectedUser.first_name || selectedUser.last_name)
                                                ? `${selectedUser.first_name || ''} ${selectedUser.last_name || ''}`.trim()
                                                : 'Unknown User'}
                                        </p>
                                        <p className="text-xs text-blue-700">
                                            Active conversation
                                        </p>
                                    </div>
                                </div>
                                {/* Listing Info - Right Side */}
                                {listingInfo && (
                                    <div className="flex items-center space-x-2 flex-shrink-0">
                                        <div className="text-right">
                                            <p className="text-xs font-medium text-blue-900 truncate max-w-24">
                                                {listingInfo.title}
                                            </p>
                                            <p className="text-xs text-blue-700">
                                                Listing
                                            </p>
                                        </div>
                                        {listingInfo.image_url && (
                                            <div className="w-8 h-8 rounded-lg overflow-hidden bg-gray-200 flex-shrink-0">
                                                <Image
                                                    src={listingInfo.image_url}
                                                    alt={listingInfo.title}
                                                    width={32}
                                                    height={32}
                                                    className="w-full h-full object-cover"
                                                />
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    {/* Conversations Accordion Toggle */}
                    <button
                        onClick={onToggleMobileAccordion}
                        className="w-full p-3 bg-white border-b border-gray-200 flex items-center justify-between hover:bg-gray-50 transition-colors"
                    >
                        <div className="flex items-center space-x-2">
                            <MessageSquare className="w-5 h-5 text-gray-600" />
                            <span className="text-sm font-medium text-gray-900">
                                {isMobileAccordionOpen ? 'Hide Conversations' : `Switch Conversations (${chatUsers.length})`}
                            </span>
                        </div>
                        {isMobileAccordionOpen ? (
                            <ChevronUp className="w-5 h-5 text-gray-600" />
                        ) : (
                            <ChevronDown className="w-5 h-5 text-gray-600" />
                        )}
                    </button>

                    {/* Conversations List - Collapsible */}
                    {isMobileAccordionOpen && (
                        <div className="bg-white">
                            {isLoading ? (
                                <div className="p-4">Loading...</div>
                            ) : (
                                <div className="max-h-64 overflow-y-auto">
                                    {chatUsers.map((user: ChatUser) => (
                                        <div
                                            key={`${user.profile.user_id}-${user.listingId}`}
                                            onClick={() => {
                                                onUserSelect(user.profile, user.listingId);
                                                // Don't auto-close accordion to allow easy switching between conversations
                                            }}
                                            className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${selectedUser?.user_id === user.profile.user_id ? 'bg-blue-50' : ''
                                                }`}
                                        >
                                            <div className="flex items-start space-x-3">
                                                <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                                                    {user.profile.profile_photo ? (
                                                        <Image
                                                            src={user.profile.profile_photo}
                                                            alt={`${user.profile.first_name || ''} ${user.profile.last_name || ''}`.trim() || 'User'}
                                                            width={40}
                                                            height={40}
                                                            className="w-full h-full object-cover"
                                                        />
                                                    ) : (
                                                        <div className="w-full h-full flex items-center justify-center">
                                                            <UserIcon className="w-6 h-6 text-gray-600" />
                                                        </div>
                                                    )}
                                                </div>
                                                <div className="flex-1 min-w-0">
                                                    <p className="text-sm font-medium text-gray-900">
                                                        {(user.profile.first_name || user.profile.last_name)
                                                            ? `${user.profile.first_name || ''} ${user.profile.last_name || ''}`.trim()
                                                            : 'Unknown User'}
                                                    </p>
                                                    <p className="text-xs text-gray-500 truncate">
                                                        {user.lastMessage.listing?.title}
                                                    </p>
                                                    <p className="text-xs text-gray-400 truncate">
                                                        {user.lastMessage.content}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>

            {/* Conversation Header - Hidden on mobile */}
            <div className="hidden md:block p-6 border-b border-gray-200 bg-gray-50">
                <div className="flex items-center justify-between">
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                            Conversation between {currentUserName} and {otherUserName}
                        </h3>
                        {listingInfo && (
                            <p className="text-sm text-gray-600 mt-1">
                                About: <span className="font-medium">{listingInfo.title || 'Listing'}</span>
                            </p>
                        )}
                    </div>
                    {listingInfo?.image_url && (
                        <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-200">
                            <Image
                                src={listingInfo.image_url}
                                alt={listingInfo.title}
                                width={48}
                                height={48}
                                className="w-full h-full object-cover"
                            />
                        </div>
                    )}
                </div>
            </div>

            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto md:p-6 p-3 space-y-4">
                {isLoading ? (
                    <div className="flex items-center justify-center h-32">
                        <div className="text-gray-500">Loading messages...</div>
                    </div>
                ) : messages.length === 0 ? (
                    <div className="flex items-center justify-center h-32">
                        <div className="text-center">
                            <MessageSquare className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                            <p className="text-gray-500">No messages yet</p>
                            <p className="text-sm text-gray-400">Start the conversation!</p>
                        </div>
                    </div>
                ) : (
                    <>
                        {messages.map((message: FormattedMessage) => {
                            const attachments = messageAttachments[message.id] || [];
                            const hasLOI = attachments.some(att => att.attachment_type === 'letter_of_intent');

                            // Render LOI messages as system notifications
                            if (hasLOI) {
                                const loiAttachment = attachments.find(att => att.attachment_type === 'letter_of_intent');
                                if (loiAttachment) {
                                    return (
                                        <SystemNotification
                                            key={message.id}
                                            message={message}
                                            attachment={loiAttachment}
                                            timestamp={new Date(message.created_at).toLocaleString()}
                                            currentUser={currentUser}
                                            listingInfo={listingInfo}
                                        />
                                    );
                                }
                            }

                            // Render regular messages
                            return (
                                <div
                                    key={message.id}
                                    className={`flex ${message.sender_id === currentUser?.id ? 'justify-end' : 'justify-start'}`}
                                >
                                    <div className={`flex items-start space-x-2 max-w-xs lg:max-w-md ${message.sender_id === currentUser?.id ? 'flex-row-reverse space-x-reverse' : ''
                                        }`}>
                                        {/* Profile photo */}
                                        <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                                            {message.sender_id === currentUser?.id ? (
                                                // Current user's photo from their profile
                                                currentUserProfile?.profile_photo ? (
                                                    <Image
                                                        src={currentUserProfile.profile_photo}
                                                        alt={`${currentUserProfile.first_name || ''} ${currentUserProfile.last_name || ''}`.trim() || 'User'}
                                                        width={32}
                                                        height={32}
                                                        className="w-full h-full object-cover"
                                                    />
                                                ) : (
                                                    <div className="w-full h-full flex items-center justify-center">
                                                        <span className="text-xs font-medium text-gray-600">
                                                            {currentUserProfile?.first_name?.[0] || ''}{currentUserProfile?.last_name?.[0] || ''}
                                                        </span>
                                                    </div>
                                                )
                                            ) : (
                                                // Other user's photo from message sender data
                                                message.sender?.profile_photo ? (
                                                    <Image
                                                        src={message.sender.profile_photo}
                                                        alt={`${message.sender.first_name || ''} ${message.sender.last_name || ''}`.trim() || 'User'}
                                                        width={32}
                                                        height={32}
                                                        className="w-full h-full object-cover"
                                                    />
                                                ) : (
                                                    <div className="w-full h-full flex items-center justify-center">
                                                        <span className="text-xs font-medium text-gray-600">
                                                            {message.sender?.first_name?.[0] || ''}{message.sender?.last_name?.[0] || ''}
                                                        </span>
                                                    </div>
                                                )
                                            )}
                                        </div>

                                        {/* Message bubble */}
                                        <div className={`p-4 rounded-lg shadow-sm ${message.sender_id === currentUser?.id
                                            ? 'bg-blue-600 text-white'
                                            : 'bg-white border border-gray-200'
                                            }`}>
                                            <div className={`text-xs mb-2 ${message.sender_id === currentUser?.id ? 'text-blue-100' : 'text-gray-500'
                                                }`}>
                                                {message.sender_id === currentUser?.id
                                                    ? 'You'
                                                    : (message.sender?.first_name || message.sender?.last_name)
                                                        ? `${message.sender?.first_name || ''} ${message.sender?.last_name || ''}`.trim()
                                                        : 'Unknown User'}
                                            </div>

                                            <p className="text-sm leading-relaxed">{message.content}</p>

                                            {/* Display non-LOI attachments */}
                                            {attachments.length > 0 && (
                                                <div className="mt-2 space-y-2">
                                                    {attachments.map(attachment => (
                                                        <MessageAttachmentDisplay
                                                            key={attachment.id}
                                                            attachment={attachment}
                                                            currentUser={currentUser}
                                                            listingInfo={listingInfo}
                                                            senderId={message.sender_id}
                                                        />
                                                    ))}
                                                </div>
                                            )}

                                            <p className={`text-xs mt-2 ${message.sender_id === currentUser?.id ? 'text-blue-100' : 'text-gray-400'
                                                }`}>
                                                {new Date(message.created_at).toLocaleString()}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                        <div ref={messagesEndRef} />
                    </>
                )}
            </div>

            {/* Desktop Actions Section - Floating outside message area */}
            {(isPotentialBuyer || (currentUser && listingInfo && listingInfo.user_id === currentUser.id && selectedUser)) && (
                <div className="hidden md:block sticky bottom-24 z-40 mx-6 mb-4 space-y-3" style={{ top: '120px' }}>
                    {/* LOI Download Section - Only show for potential buyers */}
                    {isPotentialBuyer && <LOIDownloadButton isVisible={true} />}

                    {/* DataRoom Access Toggle - Only show for listing owners */}
                    {currentUser && listingInfo && listingInfo.user_id === currentUser.id && selectedUser && (
                        <DataRoomAccessToggle
                            listingInfo={listingInfo}
                            currentUser={currentUser}
                            selectedUser={selectedUser}
                            hasDataRoomAccess={hasDataRoomAccess}
                            onAccessChange={setHasDataRoomAccess}
                        />
                    )}
                </div>
            )}

            {/* Mobile Actions Section - Floating outside message area */}
            {(isPotentialBuyer || (currentUser && listingInfo && listingInfo.user_id === currentUser.id && selectedUser)) && (
                <div className="md:hidden sticky bottom-20 z-40 mx-2 mb-2 space-y-2" style={{ top: '80px' }}>
                    {/* LOI Download Section - Only show for potential buyers */}
                    {isPotentialBuyer && <LOIDownloadButton isVisible={true} />}

                    {/* DataRoom Access Toggle - Only show for listing owners */}
                    {currentUser && listingInfo && listingInfo.user_id === currentUser.id && selectedUser && (
                        <DataRoomAccessToggle
                            listingInfo={listingInfo}
                            currentUser={currentUser}
                            selectedUser={selectedUser}
                            hasDataRoomAccess={hasDataRoomAccess}
                            onAccessChange={setHasDataRoomAccess}
                        />
                    )}
                </div>
            )}

            {/* Message Input Area */}
            <div className="sticky bottom-0 z-30 md:p-6 p-3 border-t border-gray-200 bg-gray-50">
                {/* Desktop Layout */}
                <div className="hidden md:flex space-x-4">
                    <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder={`Send a message to ${otherUserName}...`}
                        className="flex-1 rounded-lg border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <UploadDropdown onFileUpload={onFileUpload} isUploading={isUploading} uploadComplete={uploadComplete} showLOIOption={!!isPotentialBuyer} />
                    <button
                        onClick={handleSendMessage}
                        disabled={!newMessage.trim()}
                        className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                    >
                        Send
                    </button>
                </div>

                {/* Mobile Layout */}
                <div className="md:hidden space-y-2">
                    {/* Auto-expanding textarea */}
                    <textarea
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder={`Send a message to ${otherUserName}...`}
                        rows={1}
                        className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none overflow-hidden"
                        style={{
                            minHeight: '44px',
                            height: 'auto'
                        }}
                        onInput={(e) => {
                            const target = e.target as HTMLTextAreaElement;
                            target.style.height = 'auto';
                            target.style.height = target.scrollHeight + 'px';
                        }}
                    />

                    {/* Buttons row */}
                    <div className="flex space-x-2">
                        <UploadDropdown onFileUpload={onFileUpload} isUploading={isUploading} uploadComplete={uploadComplete} showLOIOption={!!isPotentialBuyer} />
                        <button
                            onClick={handleSendMessage}
                            disabled={!newMessage.trim()}
                            className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed font-medium"
                        >
                            Send Message
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default function InboxContent() {
    const router = useRouter();
    const [messages, setMessages] = useState<FormattedMessage[]>([]);
    const [chatUsers, setChatUsers] = useState<ChatUser[]>([]);
    const [selectedUser, setSelectedUser] = useState<Profile | null>(null);
    const [selectedListingId, setSelectedListingId] = useState<string | null>(null);
    const [listingInfo, setListingInfo] = useState<ListingInfo | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isMessagesLoading, setIsMessagesLoading] = useState(false);
    const [currentUser, setCurrentUser] = useState<User | null>(null);
    const [currentUserProfile, setCurrentUserProfile] = useState<Profile | null>(null);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadComplete, setUploadComplete] = useState(false);
    const [isMobileAccordionOpen, setIsMobileAccordionOpen] = useState(false);
    const [hasAutoSelected, setHasAutoSelected] = useState(false);
    const supabase = createClient();

    const handleFileUpload = async (file: File, attachmentType: 'file' | 'image' | 'letter_of_intent') => {
        if (!currentUser || !selectedUser || !selectedListingId) return;

        setIsUploading(true);
        setUploadComplete(false);

        try {
            // Generate unique filename
            const fileExtension = file.name.split('.').pop();
            const fileName = `${currentUser.id}/${Date.now()}.${fileExtension}`;

            // Upload file to Supabase storage
            const { error: uploadError } = await supabase.storage
                .from('uploads')
                .upload(fileName, file);

            if (uploadError) {
                console.error('Upload error:', uploadError);
                setIsUploading(false);
                return;
            }

            // Get public URL
            const { data: { publicUrl } } = supabase.storage
                .from('uploads')
                .getPublicUrl(fileName);

            // Get or create conversation_id (same logic as fetchMessages)
            const participantIds = [currentUser.id, selectedUser.user_id].sort();

            const { data: existingConversation } = await supabase
                .from('conversations')
                .select('id')
                .eq('listing_id', selectedListingId)
                .eq('participant1_id', participantIds[0])
                .eq('participant2_id', participantIds[1])
                .single();

            let conversationId = existingConversation?.id;

            if (!conversationId) {
                // Create new conversation
                const { data: newConversation } = await supabase
                    .from('conversations')
                    .insert({
                        listing_id: selectedListingId,
                        participant1_id: participantIds[0],
                        participant2_id: participantIds[1]
                    })
                    .select('id')
                    .single();

                conversationId = newConversation?.id;
            }

            // Create message content based on attachment type
            let messageContent = '';
            if (attachmentType === 'letter_of_intent') {
                messageContent = `${currentUserProfile?.first_name || 'User'} shared a Letter of Intent`;
            } else {
                messageContent = `${currentUserProfile?.first_name || 'User'} shared a ${attachmentType}`;
            }

            // Insert message with proper conversation_id
            const { data: messageData, error: messageError } = await supabase
                .from('messages')
                .insert([
                    {
                        conversation_id: conversationId,
                        content: messageContent,
                        sender_id: currentUser.id,
                        recipient_id: selectedUser.user_id,
                        listing_id: selectedListingId,
                    }
                ])
                .select()
                .single();

            if (messageError) {
                console.error('Message error:', messageError);
                setIsUploading(false);
                return;
            }

            // Insert attachment
            const { error: attachmentError } = await supabase
                .from('message_attachments')
                .insert([
                    {
                        message_id: messageData.id,
                        file_name: file.name,
                        file_url: publicUrl,
                        file_size: file.size,
                        file_type: file.type,
                        attachment_type: attachmentType,
                        uploaded_by: currentUser.id,
                    }
                ]);

            if (attachmentError) {
                console.error('Attachment error:', attachmentError);
                setIsUploading(false);
                return;
            }

            // If LOI was uploaded, send email notification to listing owner
            if (attachmentType === 'letter_of_intent') {
                try {
                    console.log('📧 Sending LOI email notification...');
                    const loiEmailResponse = await fetch('/api/send-loi-email', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            messageId: messageData.id,
                            attachmentData: {
                                file_name: file.name,
                                file_size: file.size,
                                file_type: file.type,
                                attachment_type: attachmentType
                            }
                        })
                    });

                    const emailResult = await loiEmailResponse.json();
                    if (emailResult.success) {
                        console.log('✅ LOI email notification sent successfully');
                    } else {
                        console.error('❌ LOI email notification failed:', emailResult.error);
                    }
                } catch (emailError) {
                    console.error('❌ LOI email notification error (non-critical):', emailError);
                }
            }

            // If LOI was uploaded, update deal status to 'initial_offer'
            if (attachmentType === 'letter_of_intent') {
                try {
                    // Get listing owner (seller)
                    const { data: listing } = await supabase
                        .from('listings')
                        .select('user_id')
                        .eq('id', selectedListingId)
                        .single();

                    if (listing?.user_id) {
                        // Upsert deal status
                        const { error: dealStatusError } = await supabase
                            .from('deal_statuses')
                            .upsert({
                                listing_id: selectedListingId,
                                buyer_id: currentUser.id,
                                seller_id: listing.user_id,
                                status: 'initial_offer',
                                status_changed_at: new Date().toISOString()
                            }, {
                                onConflict: 'listing_id,buyer_id'
                            });

                        if (dealStatusError) {
                            console.error('Deal status update error:', dealStatusError);
                        } else {
                            console.log('✅ Deal status updated to initial_offer for buyer LOI upload');
                        }
                    }
                } catch (error) {
                    console.error('Error updating deal status:', error);
                }
            }

            // Success! Show complete state
            setIsUploading(false);
            setUploadComplete(true);

            // Reset to normal state after 2 seconds
            setTimeout(() => {
                setUploadComplete(false);
            }, 2000);

            // Refresh messages
            if (selectedUser && selectedListingId) {
                await fetchMessages(selectedListingId, selectedUser.user_id);
            }

        } catch (error) {
            console.error('File upload error:', error);
            setIsUploading(false);
            setUploadComplete(false);
        }
    };

    const fetchMessages = useCallback(async (listingId: string, otherUserId: string) => {
        setIsMessagesLoading(true);
        try {
            const { data: { session } } = await supabase.auth.getSession();
            if (!session?.user?.id) return;

            const participantIds = [session.user.id, otherUserId].sort();

            // 1. First check/create conversation
            const { data: existingConversation } = await supabase
                .from('conversations')
                .select('id')
                .eq('listing_id', listingId)
                .eq('participant1_id', participantIds[0])
                .eq('participant2_id', participantIds[1])
                .single();

            let conversationId = existingConversation?.id;

            if (!conversationId) {
                // Create new conversation
                const { data: newConversation } = await supabase
                    .from('conversations')
                    .insert({
                        listing_id: listingId,
                        participant1_id: participantIds[0],
                        participant2_id: participantIds[1]
                    })
                    .select('id')
                    .single();

                conversationId = newConversation?.id;
            }

            // 2. Update any messages that are missing conversation_id
            if (conversationId) {
                const { error: updateError } = await supabase
                    .from('messages')
                    .update({ conversation_id: conversationId })
                    .eq('listing_id', listingId)
                    .or(`sender_id.eq.${participantIds[0]},recipient_id.eq.${participantIds[0]}`)
                    .or(`sender_id.eq.${participantIds[1]},recipient_id.eq.${participantIds[1]}`)
                    .is('conversation_id', null);

                if (updateError) {
                    console.error('Error updating messages:', updateError);
                }
            }

            // 3. Now fetch all messages for this conversation
            const { data: messages } = await supabase
                .from('messages')
                .select(`
                    *,
                    listings (
                        id,
                        title,
                        image_url,
                        user_id
                    )
                `)
                .eq('listing_id', listingId)
                .or(`sender_id.eq.${participantIds[0]},recipient_id.eq.${participantIds[0]}`)
                .or(`sender_id.eq.${participantIds[1]},recipient_id.eq.${participantIds[1]}`)
                .order('created_at', { ascending: true });

            if (!messages?.length) return;

            // Get listing information separately
            const { data: listing } = await supabase
                .from('listings')
                .select('id, title, image_url, user_id')
                .eq('id', listingId)
                .single();

            setListingInfo(listing);

            const { data: profiles } = await supabase
                .from('profiles')
                .select('*')
                .in('user_id', messages.map(m => m.sender_id));

            const formattedMessages = messages.map(msg => ({
                id: msg.id,
                content: msg.content,
                created_at: msg.created_at,
                sender_id: msg.sender_id,
                recipient_id: msg.recipient_id,
                listing_id: msg.listing_id,
                read: msg.read,
                sender: profiles?.find(p => p.user_id === msg.sender_id),
                conversation_id: msg.conversation_id || conversationId // Use new conversation_id if message doesn't have one
            }));

            // After fetching messages, mark them as read if user is recipient
            const { error: readError } = await supabase
                .from('messages')
                .update({ read: true })
                .eq('listing_id', listingId)
                .eq('recipient_id', session.user.id)
                .eq('read', false);

            if (readError) {
                console.error('Error marking messages as read:', readError);
            }

            setMessages(formattedMessages);

        } catch (error) {
            console.error('Error in fetchMessages:', error);
        } finally {
            setIsMessagesLoading(false);
        }
    }, [supabase]);

    const fetchChatUsers = useCallback(async () => {
        try {
            setIsLoading(true);
            const { data: { session } } = await supabase.auth.getSession();
            if (!session?.user?.id) return;

            // 1. First get all messages where the user is either sender or recipient
            const { data: userMessages } = await supabase
                .from('messages')
                .select(`
                    *,
                    listings (
                        id,
                        title,
                        image_url,
                        user_id
                    )
                `)
                .or(`sender_id.eq.${session.user.id},recipient_id.eq.${session.user.id}`);

            if (!userMessages?.length) return;

            // 2. Group messages by listing and other participant
            const messageGroups: Record<string, MessageGroup> = userMessages.reduce((acc, message) => {
                const otherParticipantId = message.sender_id === session.user.id
                    ? message.recipient_id
                    : message.sender_id;

                const key = `${message.listing_id}-${otherParticipantId}`;

                if (!acc[key]) {
                    acc[key] = {
                        listingId: message.listing_id,
                        otherParticipantId,
                        messages: [],
                        listing: message.listings
                    };
                }
                acc[key].messages.push(message);
                return acc;
            }, {});

            // 3. Get all other participants' profiles
            const otherParticipantIds = [...new Set(Object.values(messageGroups).map(g => g.otherParticipantId))];
            const { data: profiles } = await supabase
                .from('profiles')
                .select('*')
                .in('user_id', otherParticipantIds);

            // 4. Format chat users
            const formattedChatUsers = Object.values(messageGroups).map(group => {
                const latestMessage = group.messages.sort((a, b) =>
                    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                )[0];

                return {
                    profile: profiles?.find(p => p.user_id === group.otherParticipantId) || {
                        user_id: group.otherParticipantId,
                        first_name: 'Unknown',
                        last_name: 'User',
                        profile_photo: null
                    },
                    listingId: group.listingId,
                    lastMessage: {
                        created_at: latestMessage.created_at,
                        content: latestMessage.content,
                        read: latestMessage.read,
                        listing: group.listing
                    }
                };
            });

            // Sort by most recent message timestamp
            const sortedChatUsers = formattedChatUsers.sort((a, b) =>
                new Date(b.lastMessage.created_at).getTime() - new Date(a.lastMessage.created_at).getTime()
            );

            setChatUsers(sortedChatUsers);

            // Auto-select the most recent conversation if none is selected and we haven't auto-selected before
            if (!selectedUser && !hasAutoSelected && sortedChatUsers.length > 0) {
                const mostRecentUser = sortedChatUsers[0];
                setSelectedUser(mostRecentUser.profile);
                setSelectedListingId(mostRecentUser.listingId);
                setHasAutoSelected(true);
                // Fetch messages for the auto-selected conversation
                fetchMessages(mostRecentUser.listingId, mostRecentUser.profile.user_id);
            }

        } catch (error) {
            console.error('Error:', error);
        } finally {
            setIsLoading(false);
        }
    }, [supabase, selectedUser, hasAutoSelected, fetchMessages]);

    const sendMessage = async (content: string) => {
        if (!selectedUser || !selectedListingId || !currentUser) return;

        try {
            const { data: messageResult, error } = await supabase
                .from('messages')
                .insert({
                    content,
                    sender_id: currentUser.id,
                    recipient_id: selectedUser.user_id,
                    listing_id: selectedListingId,
                    read: false
                })
                .select()
                .single();

            if (error) {
                console.error('Error sending message:', error);
                return;
            }

            console.log('✅ Message sent successfully:', messageResult);

            // Trigger email notification (non-blocking)
            if (messageResult) {
                try {
                    await fetch('/api/send-message-email', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            record: messageResult
                        })
                    });
                    console.log('📧 Email notification triggered');
                } catch (emailError) {
                    console.log('📧 Email notification failed (non-critical):', emailError);
                }

                // Send Courier notification (non-blocking)
                try {
                    await sendCourierMessageNotification({
                        recipientUserId: selectedUser.user_id,
                        messageContent: content,
                        listingId: selectedListingId,
                    });
                    console.log('📱 Courier notification sent');
                } catch (courierError) {
                    console.log('📱 Courier notification failed (non-critical):', courierError);
                }
            }

            // Don't refresh messages here - let real-time subscription handle it
            fetchChatUsers(); // Only refresh the chat users list
        } catch (error) {
            console.error('Error sending message:', error);
        }
    };

    useEffect(() => {
        const checkUser = async () => {
            const { data: { session } } = await supabase.auth.getSession();
            setCurrentUser(session?.user || null);

            // Fetch current user's profile including profile_photo
            if (session?.user?.id) {
                try {
                    const { data: profile } = await supabase
                        .from('profiles')
                        .select('*')
                        .eq('user_id', session.user.id)
                        .single();

                    setCurrentUserProfile(profile);
                } catch (error) {
                    console.error('Error fetching current user profile:', error);
                    setCurrentUserProfile(null);
                }
            } else {
                setCurrentUserProfile(null);
            }
        };
        checkUser();
    }, [supabase]);

    useEffect(() => {
        fetchChatUsers();

        const messagesChannel = supabase
            .channel('inbox-messages')
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'messages'
            }, () => {
                fetchChatUsers();
            })
            .subscribe();

        const conversationsChannel = supabase
            .channel('inbox-conversations')
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'conversations'
            }, () => {
                fetchChatUsers();
            })
            .subscribe();

        return () => {
            supabase.removeChannel(messagesChannel);
            supabase.removeChannel(conversationsChannel);
        };
    }, [fetchChatUsers, supabase]);

    // NEW: Real-time subscription for current conversation messages
    useEffect(() => {
        if (!selectedListingId || !selectedUser || !currentUser) return;

        const participantIds = [currentUser.id, selectedUser.user_id].sort();

        const conversationChannel = supabase
            .channel(`conversation-${selectedListingId}-${participantIds.join('-')}`)
            .on(
                'postgres_changes',
                {
                    event: 'INSERT',
                    schema: 'public',
                    table: 'messages',
                    filter: `listing_id=eq.${selectedListingId}`
                },
                async (payload) => {
                    const newMessage = payload.new as RealtimeMessagePayload;

                    // Only add messages that belong to this conversation
                    if ((newMessage.sender_id === currentUser.id && newMessage.recipient_id === selectedUser.user_id) ||
                        (newMessage.sender_id === selectedUser.user_id && newMessage.recipient_id === currentUser.id)) {

                        // Get sender profile for the new message
                        const { data: senderProfile } = await supabase
                            .from('profiles')
                            .select('*')
                            .eq('user_id', newMessage.sender_id)
                            .single();

                        const formattedNewMessage: FormattedMessage = {
                            id: newMessage.id,
                            content: newMessage.content,
                            created_at: newMessage.created_at,
                            sender_id: newMessage.sender_id,
                            recipient_id: newMessage.recipient_id,
                            listing_id: newMessage.listing_id,
                            read: newMessage.read,
                            sender: senderProfile,
                            conversation_id: newMessage.conversation_id || ''
                        };

                        setMessages(prev => [...prev, formattedNewMessage]);

                        // Mark as read if we're the recipient
                        if (newMessage.recipient_id === currentUser.id) {
                            await supabase
                                .from('messages')
                                .update({ read: true })
                                .eq('id', newMessage.id);
                        }
                    }
                }
            )
            .subscribe();

        return () => {
            supabase.removeChannel(conversationChannel);
        };
    }, [selectedListingId, selectedUser, currentUser, supabase]);

    const handleUserSelect = (profile: Profile, listingId: string) => {
        setSelectedUser(profile);
        setSelectedListingId(listingId);
        fetchMessages(listingId, profile.user_id);
    };

    return (
        <div className="h-full flex flex-col md:flex-row relative">
            <UserList
                users={chatUsers}
                isLoading={isLoading}
                selectedUser={selectedUser}
                onUserSelect={handleUserSelect}
            />

            <div className="flex-1 w-full overflow-hidden">
                {selectedUser ? (
                    <MessageList
                        messages={messages}
                        selectedUser={selectedUser}
                        isLoading={isMessagesLoading}
                        currentUser={currentUser}
                        currentUserProfile={currentUserProfile}
                        listingInfo={listingInfo}
                        onSendMessage={sendMessage}
                        onFileUpload={handleFileUpload}
                        isUploading={isUploading}
                        uploadComplete={uploadComplete}
                        chatUsers={chatUsers}
                        isMobileAccordionOpen={isMobileAccordionOpen}
                        onToggleMobileAccordion={() => setIsMobileAccordionOpen(!isMobileAccordionOpen)}
                        onUserSelect={handleUserSelect}
                    />
                ) : (
                    <EmptyState onNewMessage={() => router.push('/listings')} />
                )}
            </div>
        </div>
    );
} 