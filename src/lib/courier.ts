import { CourierClient } from '@trycourier/courier';

const authToken = process.env.COURIER_AUTH_TOKEN;
const courierClient = new CourierClient({
    authorizationToken: authToken,
});

// Template IDs - you'll need to create these in Courier Dashboard
const MESSAGE_TEMPLATE_ID = process.env.COURIER_MESSAGE_TEMPLATE_ID || 'message-notification';
const DATA_ROOM_ACCESS_TEMPLATE_ID = process.env.COURIER_DATA_ROOM_ACCESS_TEMPLATE_ID || 'data-room-access-notification';

export interface SendMessageNotificationData {
    recipientUserId: string;
    recipientEmail: string;
    senderName: string;
    senderProfilePhoto?: string;
    messageContent: string;
    listingTitle: string;
    listingId: string;
}

export interface SendDataRoomAccessNotificationData {
    recipientUserId: string;
    recipientEmail: string;
    grantedByName: string;
    grantedByProfilePhoto?: string;
    listingTitle: string;
    listingId: string;
    listingSlug?: string;
}

/**
 * Send a message notification via Courier
 */
export const sendMessageNotification = async (data: SendMessageNotificationData) => {
    console.log('📨 [COURIER LIB] Starting sendMessageNotification...');
    console.log('📋 [COURIER LIB] Input data:', data);
    console.log('🔑 [COURIER LIB] Auth token configured:', !!authToken);
    console.log('📝 [COURIER LIB] Template ID:', MESSAGE_TEMPLATE_ID);
    
    if (!authToken) {
        console.warn('❌ [COURIER LIB] COURIER_AUTH_TOKEN not set, skipping Courier notification');
        return { success: false, error: 'COURIER_AUTH_TOKEN not configured' };
    }

    try {
        // Temporarily skip user profile creation for testing
        console.log('👤 [COURIER LIB] Skipping user profile creation for testing...');

        const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
        console.log('🌐 [COURIER LIB] Using base URL:', baseUrl);

        // Temporary test with working email from dashboard test
        const testEmail = data.recipientEmail === '<EMAIL>' 
            ? '<EMAIL>' 
            : data.recipientEmail;
        
        console.log('📧 [COURIER LIB] Using test email:', testEmail, '(original:', data.recipientEmail, ')');

        const messagePayload = {
            message: {
                to: {
                    email: testEmail,
                },
                template: MESSAGE_TEMPLATE_ID,
                data: {
                    senderName: data.senderName,
                    senderProfilePhoto: data.senderProfilePhoto,
                    messageContent: data.messageContent,
                    listingTitle: data.listingTitle,
                    listingId: data.listingId,
                    messageUrl: `${baseUrl}/account/inbox`,
                },
                routing: {
                    method: 'single' as const,
                    channels: ['inbox', 'email'], // Send to both in-app and email
                },
            },
        };
        
        console.log('📤 [COURIER LIB] Sending payload to Courier:', JSON.stringify(messagePayload, null, 2));
        
        const response = await courierClient.send(messagePayload);
        
        console.log('🎯 [COURIER LIB] Courier API response:', response);
        return { success: true, requestId: response.requestId };
    } catch (error) {
        console.error('❌ [COURIER LIB] Error sending Courier message notification:', error);
        console.error('❌ [COURIER LIB] Error details:', {
            name: error instanceof Error ? error.name : 'Unknown',
            message: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined
        });
        return { 
            success: false, 
            error: error instanceof Error ? error.message : 'Failed to send notification'
        };
    }
};

/**
 * Send a data room access notification via Courier
 */
export const sendDataRoomAccessNotification = async (data: SendDataRoomAccessNotificationData) => {
    if (!authToken) {
        console.warn('COURIER_AUTH_TOKEN not set, skipping Courier notification');
        return { success: false, error: 'COURIER_AUTH_TOKEN not configured' };
    }

    try {
        const dataRoomUrl = data.listingSlug 
            ? `${process.env.NEXT_PUBLIC_BASE_URL}/listings/${data.listingSlug}/data-room`
            : `${process.env.NEXT_PUBLIC_BASE_URL}/listings/${data.listingId}/data-room`;

        const { requestId } = await courierClient.send({
            message: {
                to: {
                    user_id: data.recipientUserId,
                    email: data.recipientEmail,
                },
                template: DATA_ROOM_ACCESS_TEMPLATE_ID,
                data: {
                    grantedByName: data.grantedByName,
                    grantedByProfilePhoto: data.grantedByProfilePhoto,
                    listingTitle: data.listingTitle,
                    listingId: data.listingId,
                    dataRoomUrl: dataRoomUrl,
                },
                routing: {
                    method: 'single' as const,
                    channels: ['inbox', 'email'], // Send to both in-app and email
                },
            },
        });

        return { success: true, requestId };
    } catch (error) {
        console.error('Error sending Courier data room access notification:', error);
        return { 
            success: false, 
            error: error instanceof Error ? error.message : 'Failed to send notification'
        };
    }
};

/**
 * Create or update a user profile in Courier
 */
export const upsertCourierUser = async (userId: string, email: string, profile?: {
    first_name?: string;
    last_name?: string;
    profile_photo?: string;
}) => {
    if (!authToken) {
        console.warn('COURIER_AUTH_TOKEN not set, skipping Courier user upsert');
        return { success: false, error: 'COURIER_AUTH_TOKEN not configured' };
    }

    try {
        await courierClient.profiles.replace(userId, {
            profile: {
                email,
                ...(profile?.first_name && { first_name: profile.first_name }),
                ...(profile?.last_name && { last_name: profile.last_name }),
                ...(profile?.profile_photo && { profile_photo: profile.profile_photo }),
            }
        });

        return { success: true };
    } catch (error) {
        console.error('Error upserting Courier user:', error);
        return { 
            success: false, 
            error: error instanceof Error ? error.message : 'Failed to upsert user'
        };
    }
}; 