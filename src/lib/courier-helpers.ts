/**
 * Helper functions to send notifications via Courier
 * These functions call the API endpoint which handles the actual Courier integration
 */

interface SendMessageNotificationParams {
    recipientUserId: string;
    messageContent: string;
    listingId: string;
}

interface SendDataRoomAccessNotificationParams {
    recipientUserId: string;
    listingId: string;
}

/**
 * Send a message notification to a user via Courier
 */
export const sendCourierMessageNotification = async (params: SendMessageNotificationParams) => {
    console.log('📨 [COURIER HELPER] Starting sendCourierMessageNotification...');
    console.log('📋 [COURIER HELPER] Parameters:', params);
    
    try {
        const payload = {
            type: 'message',
            data: params,
        };
        console.log('📤 [COURIER HELPER] Sending payload to API:', payload);
        
        const response = await fetch('/api/send-courier-notification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
        });

        console.log('📊 [COURIER HELPER] Response status:', response.status);
        console.log('📊 [COURIER HELPER] Response ok:', response.ok);

        const result = await response.json();
        console.log('📋 [COURIER HELPER] Response result:', result);
        
        if (!response.ok) {
            console.error('❌ [COURIER HELPER] Failed to send Courier message notification:', result.error);
            return { success: false, error: result.error };
        }

        console.log('🎯 [COURIER HELPER] Message notification sent successfully:', result);
        return result;
    } catch (error) {
        console.error('❌ [COURIER HELPER] Error sending Courier message notification:', error);
        return { 
            success: false, 
            error: error instanceof Error ? error.message : 'Network error'
        };
    }
};

/**
 * Send a data room access notification to a user via Courier
 */
export const sendCourierDataRoomAccessNotification = async (params: SendDataRoomAccessNotificationParams) => {
    console.log('🏠 [COURIER HELPER] Starting sendCourierDataRoomAccessNotification...');
    console.log('📋 [COURIER HELPER] Parameters:', params);
    
    try {
        const payload = {
            type: 'data_room_access',
            data: params,
        };
        console.log('📤 [COURIER HELPER] Sending payload to API:', payload);
        
        const response = await fetch('/api/send-courier-notification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
        });

        console.log('📊 [COURIER HELPER] Response status:', response.status);
        console.log('📊 [COURIER HELPER] Response ok:', response.ok);

        const result = await response.json();
        console.log('📋 [COURIER HELPER] Response result:', result);
        
        if (!response.ok) {
            console.error('❌ [COURIER HELPER] Failed to send Courier data room access notification:', result.error);
            return { success: false, error: result.error };
        }

        console.log('🎯 [COURIER HELPER] Data room access notification sent successfully:', result);
        return result;
    } catch (error) {
        console.error('❌ [COURIER HELPER] Error sending Courier data room access notification:', error);
        return { 
            success: false, 
            error: error instanceof Error ? error.message : 'Network error'
        };
    }
}; 