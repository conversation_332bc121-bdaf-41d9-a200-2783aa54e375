{"name": "supabase-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "shadcn-init": "shadcn-ui init"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.6", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.47.10", "@trycourier/courier": "^6.4.2", "@trycourier/react-inbox": "^7.4.0", "@types/google.maps": "^3.58.1", "autoprefixer": "^10.4.20", "canvas-confetti": "^1.9.3", "csv-parser": "^3.2.0", "date-fns": "^4.1.0", "framer-motion": "^12.12.1", "lucide-react": "^0.469.0", "next": "15.1.3", "openai": "^4.79.3", "papaparse": "^5.5.3", "postcss": "^8.4.49", "react": "^19.0.0", "react-confetti": "^6.4.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-tabs": "^6.1.0", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwindcss": "^3.4.17", "zod": "^3.25.36"}, "devDependencies": {"@eslint/eslintrc": "^3", "@shadcn/ui": "^0.0.4", "@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/react": "^19", "@types/react-dom": "^19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9", "eslint-config-next": "15.1.3", "ignore-loader": "^0.1.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "typescript": "^5"}}