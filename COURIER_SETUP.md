# Courier Notifications Setup Guide

This guide will walk you through setting up Courier notifications for your marketplace application.

## Prerequisites

1. A [Courier account](https://app.courier.com/signup)
2. Your app already has the `@trycourier/courier` and `@trycourier/react-inbox` packages installed

## Step 1: Install Courier Packages

If you haven't already, install the required packages:

```bash
npm install @trycourier/courier @trycourier/react-inbox
```

## Step 2: Get Your Courier Credentials

1. Log into your [Courier Dashboard](https://app.courier.com)
2. Go to Settings → API Keys
3. Copy your **Auth Token** and **Client Key**

## Step 3: Set Environment Variables

Add these to your `.env.local` file:

```bash
# Courier Configuration
COURIER_AUTH_TOKEN=your_courier_auth_token_here
COURIER_MESSAGE_TEMPLATE_ID=message-notification
COURIER_DATA_ROOM_ACCESS_TEMPLATE_ID=data-room-access-notification
NEXT_PUBLIC_COURIER_CLIENT_KEY=your_courier_client_key_here

# Base URL for your application
NEXT_PUBLIC_BASE_URL=http://localhost:3000  # Change to your production URL in production
```

## Step 4: Create Notification Templates in Courier

You need to create two notification templates in your Courier Dashboard:

### 1. Message Notification Template

1. Go to **Designer** → **Notifications** in Courier Dashboard
2. Click **Create Notification**
3. Name it `message-notification` (or update the env var to match)
4. Configure the template with these data variables:
   - `senderName` - Name of the message sender
   - `senderProfilePhoto` - URL to sender's profile photo (optional)
   - `messageContent` - The message content
   - `listingTitle` - Title of the listing being discussed
   - `messageUrl` - Link to the inbox

**Example template content:**
```
Subject: New message from {{senderName}}

{{senderName}} sent you a message about "{{listingTitle}}":

"{{messageContent}}"

[View Message]({{messageUrl}})
```

### 2. Data Room Access Notification Template

1. Create another notification called `data-room-access-notification`
2. Configure with these data variables:
   - `grantedByName` - Name of the person who granted access
   - `grantedByProfilePhoto` - URL to granter's profile photo (optional)
   - `listingTitle` - Title of the listing
   - `dataRoomUrl` - Link to the data room

**Example template content:**
```
Subject: Data room access granted for {{listingTitle}}

{{grantedByName}} has granted you access to the data room for "{{listingTitle}}".

[Access Data Room]({{dataRoomUrl}})
```

## Step 5: Configure Notification Channels

For each template, set up the channels you want to use:

1. **In-App Inbox** - For real-time notifications in your app
2. **Email** - For email notifications
3. **SMS** (optional) - For SMS notifications

## Step 6: Use the Enhanced Notification Bell

The `EnhancedNotificationBell` component already supports Courier. To use it:

```tsx
import { EnhancedNotificationBell } from '@/components/notifications/EnhancedNotificationBell';

// In your component
<EnhancedNotificationBell 
    useCourier={true}  // Enable Courier mode
    className="relative p-2"
/>
```

## Step 7: Test the Integration

1. Start your development server
2. Send a message between users - you should see:
   - A Courier notification sent (check browser console)
   - Email notification (if email channel is configured)
   - In-app notification (if using `useCourier={true}`)

3. Grant data room access - you should see:
   - A Courier notification sent (check browser console)
   - Email notification (if configured)

## How It Works

### Current Implementation

The integration automatically sends Courier notifications when:

1. **Messages are sent** (`src/app/messages/components/InboxContent.tsx`)
   - Calls `sendCourierMessageNotification()` after successfully sending a message
   - Includes sender info, message content, and listing details

2. **Data room access is granted** (`src/app/messages/components/InboxContent.tsx`)
   - Calls `sendCourierDataRoomAccessNotification()` after granting access
   - Includes granter info, listing details, and data room URL

### Files Created/Modified

- `src/lib/courier.ts` - Core Courier integration functions
- `src/lib/courier-helpers.ts` - Client-side helper functions  
- `src/app/api/send-courier-notification/route.ts` - API endpoint for sending notifications
- `src/app/messages/components/InboxContent.tsx` - Updated to send notifications
- `src/components/notifications/EnhancedNotificationBell.tsx` - Updated with proper TypeScript types

## Customization

### Adding More Notification Types

1. Add new functions to `src/lib/courier.ts`
2. Add corresponding helpers to `src/lib/courier-helpers.ts`
3. Update the API route in `src/app/api/send-courier-notification/route.ts`
4. Create templates in Courier Dashboard
5. Call the helper functions where needed in your app

### Customizing Templates

Edit your templates in the Courier Dashboard Designer to:
- Change the email/SMS content
- Add images or branding
- Modify the data variables
- Set up different channels

## Troubleshooting

### Common Issues

1. **"COURIER_AUTH_TOKEN not configured"**
   - Make sure you've set the environment variable
   - Restart your development server after adding env vars

2. **Template not found errors**
   - Verify template IDs match your environment variables
   - Check that templates exist in your Courier Dashboard

3. **Notifications not appearing**
   - Check browser console for errors
   - Verify Courier client key is set for in-app notifications
   - Check that user profiles have email addresses

### Debug Mode

The integration includes console logging. Check your browser/server console for:
- `📱 Courier notification sent` - Success messages
- `📱 Courier notification failed` - Error messages

## Production Deployment

Before deploying to production:

1. Set `NEXT_PUBLIC_BASE_URL` to your production domain
2. Use production Courier credentials
3. Test all notification types
4. Configure production email/SMS providers in Courier

## Resources

- [Courier Documentation](https://www.courier.com/docs/)
- [Courier React Components](https://www.courier.com/docs/guides/providers/direct-message/react-inbox/)
- [Next.js Integration Guide](https://knock.app/blog/how-to-send-in-app-notifications-nextjs13) 